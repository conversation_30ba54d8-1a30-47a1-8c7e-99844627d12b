{"__meta": {"id": "01K0NHW4EHR3ZFS39SF3CSB9P4", "datetime": "2025-07-21 09:54:07", "utime": **********.698059, "method": "GET", "uri": "/api/frontend/country-code", "ip": "::1"}, "messages": {"count": 37, "messages": [{"message": "[09:54:07] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 457", "message_html": null, "is_string": false, "label": "warning", "time": **********.368536, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetGet($key) should either be compatible with ArrayAccess::offsetGet(mixed $offset): mixed, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 468", "message_html": null, "is_string": false, "label": "warning", "time": **********.368996, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetSet($key, $value) should either be compatible with ArrayAccess::offsetSet(mixed $offset, mixed $value): void, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 484", "message_html": null, "is_string": false, "label": "warning", "time": **********.369319, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetUnset($key) should either be compatible with ArrayAccess::offsetUnset(mixed $offset): void, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 499", "message_html": null, "is_string": false, "label": "warning", "time": **********.369624, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::count() should either be compatible with Countable::count(): int, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 593", "message_html": null, "is_string": false, "label": "warning", "time": **********.369929, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::getIterator() should either be compatible with IteratorAggregate::getIterator(): Traversable, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 605", "message_html": null, "is_string": false, "label": "warning", "time": **********.370235, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::jsonSerialize() should either be compatible with JsonSerializable::jsonSerialize(): mixed, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 579", "message_html": null, "is_string": false, "label": "warning", "time": **********.370536, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.470848, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.474402, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.477212, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.498676, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.525938, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.532054, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.584675, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.58805, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.620945, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.627654, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.648866, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.67639, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.677066, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.677738, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.678423, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.679092, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.679761, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.680451, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.681116, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.681806, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.68248, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.683162, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.683833, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.684503, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.68517, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.685851, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.68653, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.687205, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.687889, "xdebug_link": null, "collector": "log"}, {"message": "[09:54:07] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.688564, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.043341, "end": **********.698143, "duration": 0.6548020839691162, "duration_str": "655ms", "measures": [{"label": "Booting", "start": **********.043341, "relative_start": 0, "end": **********.313271, "relative_end": **********.313271, "duration": 0.****************, "duration_str": "270ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.313287, "relative_start": 0.****************, "end": **********.698146, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "385ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.345069, "relative_start": 0.****************, "end": **********.35163, "relative_end": **********.35163, "duration": 0.0065610408782958984, "duration_str": "6.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.691095, "relative_start": 0.****************, "end": **********.694985, "relative_end": **********.694985, "duration": 0.003889799118041992, "duration_str": "3.89ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.695019, "relative_start": 0.****************, "end": **********.695046, "relative_end": **********.695046, "duration": 2.6941299438476562e-05, "duration_str": "27μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "72MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/country-code", "action_name": "frontend.country-code.", "controller_action": "App\\Http\\Controllers\\Frontend\\CountryCodeController@index", "uri": "GET api/frontend/country-code", "controller": "App\\Http\\Controllers\\Frontend\\CountryCodeController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FCountryCodeController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/country-code", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FCountryCodeController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/CountryCodeController.php:19-26</a>", "middleware": "api, installed, apiKey, localization", "duration": "812ms", "peak_memory": "76MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-842212859 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-842212859\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1038168115 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1038168115\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-418304356 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">http://localhost:8000/login</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"781 characters\">XSRF-TOKEN=eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6InoybndEdnhHdnpHdVFCbjdaSzZDTlE9PSIsInZhbHVlIjoiZ3g0SXFkNU02dW9HYzh6VUYrbVgzVEVpeEpkTWMzbEZuVjdKQW1QVHFLRzJ5OWVYUUMxekk4aUtoVDFKaDBoR094eFF3VmJXMFY1VVZMUEVnWklrTXlKWkkwaWhCcmhueCtXODRkOXRxS05PaTRKMzZqQml2RTkrZ0tLTy9GdXgiLCJtYWMiOiJlYjdiNjlkOThmZTdjYWEwNzk5N2Y3NTcxMTgwNzJjNjQ1ZmJmMDQzMmJkMjcxZDIwYTJiYWE3NDViNWE4ZjBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-418304356\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1615977277 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InoybndEdnhHdnpHdVFCbjdaSzZDTlE9PSIsInZhbHVlIjoiZ3g0SXFkNU02dW9HYzh6VUYrbVgzVEVpeEpkTWMzbEZuVjdKQW1QVHFLRzJ5OWVYUUMxekk4aUtoVDFKaDBoR094eFF3VmJXMFY1VVZMUEVnWklrTXlKWkkwaWhCcmhueCtXODRkOXRxS05PaTRKMzZqQml2RTkrZ0tLTy9GdXgiLCJtYWMiOiJlYjdiNjlkOThmZTdjYWEwNzk5N2Y3NTcxMTgwNzJjNjQ1ZmJmMDQzMmJkMjcxZDIwYTJiYWE3NDViNWE4ZjBhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1615977277\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-575613036 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:54:07 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-575613036\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-864399873 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-864399873\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/country-code", "action_name": "frontend.country-code.", "controller_action": "App\\Http\\Controllers\\Frontend\\CountryCodeController@index"}, "badge": null}}