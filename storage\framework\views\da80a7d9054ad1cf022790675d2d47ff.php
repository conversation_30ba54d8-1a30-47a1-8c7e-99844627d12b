<?php $stripeKey = ""; ?>
<?php if(!blank($paymentGateways)): ?>
    <?php $__currentLoopData = $paymentGateways; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $paymentGateway): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($paymentGateway->slug === 'stripe'): ?>
            <?php $paymentGatewayOption = $paymentGateway->gatewayOptions->pluck('value', 'option'); $stripeKey =$paymentGatewayOption['stripe_key'] ?>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>

<script src="https://js.stripe.com/v3/"></script>
<script>
    const stripeKey = '<?=$stripeKey?>';
</script>
<script src="<?php echo e(asset('paymentGateways/stripe/stripe.js')); ?>"></script>
<?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\resources\views\paymentGateways\stripe\stripeJs.blade.php ENDPATH**/ ?>