{"__meta": {"id": "01K0NH7P9H9B4NAJ7CTV4B837C", "datetime": "2025-07-21 09:42:57", "utime": **********.842693, "method": "GET", "uri": "/api/frontend/product-brand?paginate=0&order_column=id&order_type=asc&status=5", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.248356, "end": **********.842726, "duration": 0.5943698883056641, "duration_str": "594ms", "measures": [{"label": "Booting", "start": **********.248356, "relative_start": 0, "end": **********.716057, "relative_end": **********.716057, "duration": 0.****************, "duration_str": "468ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.716081, "relative_start": 0.****************, "end": **********.842731, "relative_end": 5.0067901611328125e-06, "duration": 0.*****************, "duration_str": "127ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.756377, "relative_start": 0.****************, "end": **********.771309, "relative_end": **********.771309, "duration": 0.014931917190551758, "duration_str": "14.93ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.833041, "relative_start": 0.****************, "end": **********.839289, "relative_end": **********.839289, "duration": 0.006247997283935547, "duration_str": "6.25ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.839353, "relative_start": 0.****************, "end": **********.839394, "relative_end": **********.839394, "duration": 4.100799560546875e-05, "duration_str": "41μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.0038900000000000002, "accumulated_duration_str": "3.89ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `product_brands` where (`status` like '%5%') order by `id` asc", "type": "query", "params": [], "bindings": ["%5%"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ProductBrandService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\ProductBrandService.php", "line": 56}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/ProductBrandController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\ProductBrandController.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.819207, "duration": 0.0038900000000000002, "duration_str": "3.89ms", "memory": 0, "memory_str": null, "filename": "ProductBrandService.php:56", "source": {"index": 15, "namespace": null, "name": "app/Services/ProductBrandService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\ProductBrandService.php", "line": 56}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FServices%2FProductBrandService.php&line=56", "ajax": false, "filename": "ProductBrandService.php", "line": "56"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product-brand?order_column=id&order_type=asc&paginate=0&status=5", "action_name": "frontend.product-brand.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductBrandController@index", "uri": "GET api/frontend/product-brand", "controller": "App\\Http\\Controllers\\Frontend\\ProductBrandController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductBrandController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/product-brand", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductBrandController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ProductBrandController.php:20-27</a>", "middleware": "api, installed, apiKey, localization", "duration": "790ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1979739019 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>paginate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>order_column</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order_type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1979739019\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1931230062 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1931230062\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1279006977 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlJGMlQ2TFV3Y1VXYUNHbDhKejNOT0E9PSIsInZhbHVlIjoiK0lGQU9kbGxHV3dtclgyVHd1N1k3djZRV0tJZm8zeGc1Rm9DQkpReFRDU3pnTnRvcWRIdWdjMlJqeE5UdjFkcWhSNXBuN1ZRd05Xb0d6VldJVUZiMUFNYitRT0RBaXgyUDdXeFhVcm1VUWtoNlhBWHY1Nyt4YWNJTS92Wk5WVnUiLCJtYWMiOiI5NDc1Y2Y3YTZlODFmMTJlNzNhMGVjZGJlZTQxMzhjNzRlZWM3YzJlZTZhNzM5ZDFjNGQ1ZjFjZjhjNGYzYzIzIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6IlJGMlQ2TFV3Y1VXYUNHbDhKejNOT0E9PSIsInZhbHVlIjoiK0lGQU9kbGxHV3dtclgyVHd1N1k3djZRV0tJZm8zeGc1Rm9DQkpReFRDU3pnTnRvcWRIdWdjMlJqeE5UdjFkcWhSNXBuN1ZRd05Xb0d6VldJVUZiMUFNYitRT0RBaXgyUDdXeFhVcm1VUWtoNlhBWHY1Nyt4YWNJTS92Wk5WVnUiLCJtYWMiOiI5NDc1Y2Y3YTZlODFmMTJlNzNhMGVjZGJlZTQxMzhjNzRlZWM3YzJlZTZhNzM5ZDFjNGQ1ZjFjZjhjNGYzYzIzIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IlltZlFVSXF6ZjhWQVlzYkljdWkzWUE9PSIsInZhbHVlIjoiZTRremRlL0M1SjNQb21JdHhTTk1teEtkdXVSVGdNbllubEhieXlud2VNS0NiMTB3NWF2KzNzMGV2TzRBMEt4cTBQay93YWpWMDBNMVJMWU53NnRCdVpHWW5NQ1YwbFJROXdpQ2QvUE5NRkFvamwyZlBEdHdZL0M4ZmhGM1AwVTQiLCJtYWMiOiI4N2U3MGZjZmZjYzc4MzBlYzcyZGRhY2I0ZjViMTFmNTkwYTg0ODA4N2I5YjU3ZDA5ZjNiOTNkNDM1YzZjNThiIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1279006977\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-915445080 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlJGMlQ2TFV3Y1VXYUNHbDhKejNOT0E9PSIsInZhbHVlIjoiK0lGQU9kbGxHV3dtclgyVHd1N1k3djZRV0tJZm8zeGc1Rm9DQkpReFRDU3pnTnRvcWRIdWdjMlJqeE5UdjFkcWhSNXBuN1ZRd05Xb0d6VldJVUZiMUFNYitRT0RBaXgyUDdXeFhVcm1VUWtoNlhBWHY1Nyt4YWNJTS92Wk5WVnUiLCJtYWMiOiI5NDc1Y2Y3YTZlODFmMTJlNzNhMGVjZGJlZTQxMzhjNzRlZWM3YzJlZTZhNzM5ZDFjNGQ1ZjFjZjhjNGYzYzIzIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IlltZlFVSXF6ZjhWQVlzYkljdWkzWUE9PSIsInZhbHVlIjoiZTRremRlL0M1SjNQb21JdHhTTk1teEtkdXVSVGdNbllubEhieXlud2VNS0NiMTB3NWF2KzNzMGV2TzRBMEt4cTBQay93YWpWMDBNMVJMWU53NnRCdVpHWW5NQ1YwbFJROXdpQ2QvUE5NRkFvamwyZlBEdHdZL0M4ZmhGM1AwVTQiLCJtYWMiOiI4N2U3MGZjZmZjYzc4MzBlYzcyZGRhY2I0ZjViMTFmNTkwYTg0ODA4N2I5YjU3ZDA5ZjNiOTNkNDM1YzZjNThiIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915445080\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1059370346 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:42:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1059370346\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1823833096 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1823833096\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product-brand?order_column=id&order_type=asc&paginate=0&status=5", "action_name": "frontend.product-brand.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductBrandController@index"}, "badge": null}}