{"__meta": {"id": "01K0NHWP45YMQCF32RAYRE47EP", "datetime": "2025-07-21 09:54:25", "utime": **********.798115, "method": "GET", "uri": "/api/frontend/slider?paginate=0&order_column=id&order_type=desc&status=5", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.529913, "end": **********.79813, "duration": 0.2682170867919922, "duration_str": "268ms", "measures": [{"label": "Booting", "start": **********.529913, "relative_start": 0, "end": **********.749434, "relative_end": **********.749434, "duration": 0.*****************, "duration_str": "220ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.749446, "relative_start": 0.*****************, "end": **********.798132, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "48.69ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.773601, "relative_start": 0.*****************, "end": **********.780147, "relative_end": **********.780147, "duration": 0.****************, "duration_str": "6.55ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.795252, "relative_start": 0.****************, "end": **********.795816, "relative_end": **********.795816, "duration": 0.0005638599395751953, "duration_str": "564μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "400 Bad Request", "full_url": "http://localhost:8000/api/frontend/slider?order_column=id&order_type=desc&paginate=0&status=5", "action_name": "frontend.slider.", "controller_action": "App\\Http\\Controllers\\Frontend\\SliderController@index", "uri": "GET api/frontend/slider", "controller": "App\\Http\\Controllers\\Frontend\\SliderController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FSliderController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/slider", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FSliderController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/SliderController.php:20-27</a>", "middleware": "api, installed, apiKey, localization", "duration": "380ms", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1773853075 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>paginate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>order_column</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order_type</span>\" => \"<span class=sf-dump-str title=\"4 characters\">desc</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1773853075\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2117222402 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2117222402\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-368906638 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">pt</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"781 characters\">XSRF-TOKEN=eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6InoybndEdnhHdnpHdVFCbjdaSzZDTlE9PSIsInZhbHVlIjoiZ3g0SXFkNU02dW9HYzh6VUYrbVgzVEVpeEpkTWMzbEZuVjdKQW1QVHFLRzJ5OWVYUUMxekk4aUtoVDFKaDBoR094eFF3VmJXMFY1VVZMUEVnWklrTXlKWkkwaWhCcmhueCtXODRkOXRxS05PaTRKMzZqQml2RTkrZ0tLTy9GdXgiLCJtYWMiOiJlYjdiNjlkOThmZTdjYWEwNzk5N2Y3NTcxMTgwNzJjNjQ1ZmJmMDQzMmJkMjcxZDIwYTJiYWE3NDViNWE4ZjBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-368906638\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1191748507 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InoybndEdnhHdnpHdVFCbjdaSzZDTlE9PSIsInZhbHVlIjoiZ3g0SXFkNU02dW9HYzh6VUYrbVgzVEVpeEpkTWMzbEZuVjdKQW1QVHFLRzJ5OWVYUUMxekk4aUtoVDFKaDBoR094eFF3VmJXMFY1VVZMUEVnWklrTXlKWkkwaWhCcmhueCtXODRkOXRxS05PaTRKMzZqQml2RTkrZ0tLTy9GdXgiLCJtYWMiOiJlYjdiNjlkOThmZTdjYWEwNzk5N2Y3NTcxMTgwNzJjNjQ1ZmJmMDQzMmJkMjcxZDIwYTJiYWE3NDViNWE4ZjBhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1191748507\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1049767841 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:54:25 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1049767841\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1739999773 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1739999773\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "400 Bad Request", "full_url": "http://localhost:8000/api/frontend/slider?order_column=id&order_type=desc&paginate=0&status=5", "action_name": "frontend.slider.", "controller_action": "App\\Http\\Controllers\\Frontend\\SliderController@index"}, "badge": "400 Bad Request"}}