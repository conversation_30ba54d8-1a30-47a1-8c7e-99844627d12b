<?php $razorpayKey = ""; ?>
<?php if(!blank($paymentGateways)): ?>
    <?php $__currentLoopData = $paymentGateways; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $paymentGateway): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <?php if($paymentGateway->slug === 'razorpay'): ?>
            <?php $paymentGatewayOption = $paymentGateway->gatewayOptions->pluck('value', 'option'); $razorpayKey = $paymentGatewayOption['razorpay_key']; ?>
        <?php endif; ?>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
<?php endif; ?>

<script src="https://checkout.razorpay.com/v1/checkout.js"></script>

<script>
    const razorpayKey          = '<?= $razorpayKey ?>';
    const razorpayTotalAmount  = '<?= $order->total ?>';
    const razorpayCurrencyCode = '<?= $currency->code ?>';
    const razorpayCompany      = '<?= $company['company_name'] ?>';
    const razorpayLogo         = '<?= $logo->logo ?>';
    const razorpayUserName     = '<?= $order->user?->name ?>';
    const razorpayUserEmail    = '<?= $order->user?->email ?>';
    const razorpayPayLink      = '<?= route('payment.store', ['order' => $order]) ?>';
    const razorpaySuccessLink  = '<?= route('payment.successful', ['order' => $order]) ?>';
    const razorpayCancelLink   = '<?= route('payment.cancel', ['order' => $order, 'paymentGateway' => 'razorpay']) ?>';
</script>
<script src="<?php echo e(asset('paymentGateways/razorpay/razorpay.js')); ?>"></script>
<?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\resources\views\paymentGateways\razorpay\razorpayJs.blade.php ENDPATH**/ ?>