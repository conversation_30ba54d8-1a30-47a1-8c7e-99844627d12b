<?php $__env->startSection('template_title'); ?>
    <?php echo e(trans('installer.final.templateTitle')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('title'); ?>
    <?php echo e(trans('installer.final.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('container'); ?>
    <ul class="installer-track">
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.index')); ?>')" class="done">
            <i class="fa-solid fa-house"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.requirement')); ?>')" class="done">
            <i class="fa-solid fa-server"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.permission')); ?>')" class="done">
            <i class="fa-sharp fa-solid fa-unlock"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.license')); ?>')" class="done">
            <i class="fa-solid fa-key"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.site')); ?>')" class="done">
            <i class="fa-solid fa-gear"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.database')); ?>')" class="done">
            <i class="fa-solid fa-database"></i>
        </li>
        <li class="active"><i class="fa-solid fa-unlock-keyhole"></i></li>
    </ul>

    <span class="my-6 w-full h-[1px] bg-[#EFF0F6]"></span>

    <h3 class="text-lg font-medium text-center mb-8 text-[#1AB759]"><?php echo e(trans('installer.final.success_message')); ?></h3>

    <?php if($errors->has('global')): ?>
        <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 mb-5 rounded relative" role="alert">
            <span class="block sm:inline text-[#E93C3C]"><?php echo e($errors->first('global')); ?></span>
            <span class="absolute top-0 bottom-0 right-0 px-4 py-3 cursor-pointer close-alert-button">
                <i class="fa fa-close margin-top-5-px"></i>
            </span>
        </div>
    <?php endif; ?>
    <dl class="w-full max-w-sm mx-auto mb-8 rounded-lg bg-[#F7F7FC]">
        <dt class="text-sm font-medium py-3 px-4 border-b border-[#D9DBE9] text-heading">
            <?php echo e(trans('installer.final.login_info')); ?></dt>
        <dd class="py-1.5 px-4">
            <div class="text-sm text-heading py-1.5">
                <span class="w-20"><?php echo e(trans('installer.final.email')); ?>:</span>
                <span class="font-semibold"><?php echo e(trans('installer.final.email_info')); ?></span>
            </div>
            <div class="text-sm text-heading py-1.5">
                <span class="w-20"><?php echo e(trans('installer.final.password')); ?>:</span>
                <span class="font-semibold"><?php echo e(trans('installer.final.password_info')); ?></span>
            </div>
        </dd>
    </dl>

    <div class="text-center">
        <a href="<?php echo e(route('installer.finalStore')); ?>"
            class="p-3 px-6 rounded-lg inline-flex items-center justify-center gap-3 bg-primary text-white">
            <?php echo e(trans('installer.final.next')); ?>

            <i class="fa-solid fa-angle-right text-sm"></i>
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installer.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\resources\views\installer\final.blade.php ENDPATH**/ ?>