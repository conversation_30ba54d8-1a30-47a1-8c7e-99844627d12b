<?php
/**
 * Script para criar uma nova instalação completa do Shopperzz
 * Frontend + Admin + PWA + Português
 */

echo "=== CRIAÇÃO NOVA INSTALAÇÃO SHOPPERZZ ===\n\n";

$sourceDir = 'c:\Users\<USER>\Desktop\xamp8.1\htdocs';
$newDir = 'c:\Users\<USER>\Desktop\xamp8.1\shopperzz-novo';
$phpPath = 'C:\xampp\php\php.exe';

echo "📁 Diretório origem: {$sourceDir}\n";
echo "📁 Novo diretório: {$newDir}\n\n";

// Etapa 1: Criar novo diretório
echo "🔧 ETAPA 1: CRIANDO NOVO DIRETÓRIO\n";

if (is_dir($newDir)) {
    echo "⚠️ Diretório já existe. Removendo...\n";
    // Remover diretório existente (Windows)
    exec("rmdir /s /q \"{$newDir}\"", $output, $returnCode);
    if ($returnCode === 0) {
        echo "✅ Diretório antigo removido\n";
    }
}

// Criar novo diretório
if (!mkdir($newDir, 0755, true)) {
    echo "❌ Erro ao criar diretório: {$newDir}\n";
    exit(1);
}
echo "✅ Novo diretório criado: {$newDir}\n\n";

// Etapa 2: Copiar arquivos
echo "🔧 ETAPA 2: COPIANDO ARQUIVOS\n";
echo "📋 Copiando todos os arquivos... (pode demorar alguns minutos)\n";

// Usar robocopy no Windows para cópia eficiente
$robocopyCmd = "robocopy \"{$sourceDir}\" \"{$newDir}\" /E /XD node_modules .git vendor storage\\logs storage\\framework\\cache storage\\framework\\sessions storage\\framework\\views /XF *.log";
exec($robocopyCmd, $output, $returnCode);

// Robocopy retorna códigos diferentes (0-7 são sucessos)
if ($returnCode <= 7) {
    echo "✅ Arquivos copiados com sucesso\n";
} else {
    echo "❌ Erro na cópia de arquivos (código: {$returnCode})\n";
    // Continuar mesmo assim
}

echo "\n🔧 ETAPA 3: CONFIGURANDO NOVO AMBIENTE\n";

// Mudar para o novo diretório
chdir($newDir);
echo "📂 Mudando para: " . getcwd() . "\n";

// Criar .env personalizado
echo "⚙️ Criando arquivo .env personalizado...\n";
$envContent = <<<ENV
APP_NAME="Shopperzz - Nova Loja"
APP_ENV=local
APP_KEY=base64:e7gBPVzThPSkHj28dl+67KoSh7dMYnhyOpgQCAJWSDE=
APP_DEBUG=true
APP_URL=http://localhost:8001

LOG_CHANNEL=stack
LOG_DEPRECATIONS_CHANNEL=null
LOG_LEVEL=debug

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_PORT=3306
DB_DATABASE=shopperzz_novo
DB_USERNAME=root
DB_PASSWORD=

BROADCAST_DRIVER=log
CACHE_DRIVER=file
FILESYSTEM_DISK=local
QUEUE_CONNECTION=sync
SESSION_DRIVER=file
SESSION_LIFETIME=120

MEMCACHED_HOST=127.0.0.1

REDIS_HOST=127.0.0.1
REDIS_PASSWORD=null
REDIS_PORT=6379

MAIL_MAILER=smtp
MAIL_HOST=mailpit
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=null
MAIL_FROM_ADDRESS="<EMAIL>"
MAIL_FROM_NAME="Shopperzz Nova Loja"

FCM_SECRET_KEY=AAAAR-ItgeQ:APA91bHSRHexFge83tV33p9xiV0qyQ-naPZJj8TnCM9xg9gq4c_fyn30LP-x81SEnQDTbPFrqiMHkf7WSYnpM18Zb9uccmJX2wI6d1-DhAB13Kf6WFoUsPeDd07MdAEKTauVAGUD_d_J
FCM_TOPIC=

DEMO=false
TIMEZONE=America/Sao_Paulo

MIX_HOST=http://localhost:8001
MIX_API_KEY=SHOPPERZZ-2025-FREE-LICENSE
MIX_DEMO=false

CURRENCY=BRL
CURRENCY_SYMBOL=R$
CURRENCY_POSITION=5
CURRENCY_DECIMAL_POINT=2

DATE_FORMAT=d/m/Y
TIME_FORMAT="H:i"

DISPLAY_TYPE=fashion
NON_PURCHASE_QUANTITY=100
MEDIA_DISK=public
ENV;

file_put_contents('.env', $envContent);
echo "✅ Arquivo .env criado\n";

// Criar diretórios necessários
echo "📁 Criando diretórios necessários...\n";
$dirs = [
    'storage/logs',
    'storage/framework/cache',
    'storage/framework/sessions', 
    'storage/framework/views',
    'storage/app/public',
    'bootstrap/cache',
    'public/storage'
];

foreach ($dirs as $dir) {
    if (!is_dir($dir)) {
        mkdir($dir, 0755, true);
        echo "✅ Criado: {$dir}\n";
    }
}

echo "\n🔧 ETAPA 4: INSTALANDO DEPENDÊNCIAS\n";

// Verificar se composer existe
if (file_exists('composer.json')) {
    echo "📦 Instalando dependências PHP...\n";
    exec("{$phpPath} -d memory_limit=512M composer.phar install --no-dev --optimize-autoloader", $output, $returnCode);
    if ($returnCode === 0) {
        echo "✅ Dependências PHP instaladas\n";
    } else {
        echo "⚠️ Erro nas dependências PHP, continuando...\n";
    }
} else {
    echo "⚠️ composer.json não encontrado\n";
}

echo "\n🔧 ETAPA 5: CONFIGURANDO BANCO DE DADOS\n";

// Criar banco de dados
echo "🗄️ Criando banco de dados 'shopperzz_novo'...\n";
$createDbCmd = "{$phpPath} -r \"
try {
    \$pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    \$pdo->exec('CREATE DATABASE IF NOT EXISTS shopperzz_novo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    echo 'Banco de dados criado com sucesso';
} catch (Exception \$e) {
    echo 'Erro: ' . \$e->getMessage();
}
\"";

exec($createDbCmd, $output, $returnCode);
if ($returnCode === 0) {
    echo "✅ Banco de dados 'shopperzz_novo' criado\n";
} else {
    echo "❌ Erro ao criar banco de dados\n";
}

echo "\n🔧 ETAPA 6: EXECUTANDO MIGRAÇÕES\n";

// Limpar cache primeiro
exec("{$phpPath} artisan config:clear", $output, $returnCode);
exec("{$phpPath} artisan cache:clear", $output, $returnCode);
echo "✅ Cache limpo\n";

// Executar migrações
echo "🗄️ Executando migrações...\n";
exec("{$phpPath} artisan migrate --force", $output, $returnCode);
if ($returnCode === 0) {
    echo "✅ Migrações executadas\n";
} else {
    echo "⚠️ Erro nas migrações, continuando...\n";
}

// Executar seeders
echo "🌱 Executando seeders...\n";
exec("{$phpPath} artisan db:seed --force", $output, $returnCode);
if ($returnCode === 0) {
    echo "✅ Seeders executados\n";
} else {
    echo "⚠️ Erro nos seeders, continuando...\n";
}

echo "\n🔧 ETAPA 7: CONFIGURANDO PORTUGUÊS\n";

// Instalar idioma português
echo "🇧🇷 Instalando idioma português...\n";

// Criar seeder do português
$portugueseSeeder = <<<PHP
<?php
namespace Database\Seeders;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class NovoPortugueseSeeder extends Seeder
{
    public function run()
    {
        // Remover outros idiomas
        DB::table('languages')->whereNotIn('code', ['pt'])->delete();
        
        // Inserir ou atualizar português
        DB::table('languages')->updateOrInsert(
            ['code' => 'pt'],
            [
                'name' => 'Português',
                'code' => 'pt',
                'display_mode' => 1,
                'status' => 5,
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]
        );
        
        echo "✅ Idioma português configurado como único idioma\n";
    }
}
PHP;

file_put_contents('database/seeders/NovoPortugueseSeeder.php', $portugueseSeeder);
exec("{$phpPath} artisan db:seed --class=NovoPortugueseSeeder", $output, $returnCode);

echo "\n🔧 ETAPA 8: CRIANDO LINKS SIMBÓLICOS\n";

// Criar link simbólico para storage
exec("{$phpPath} artisan storage:link", $output, $returnCode);
if ($returnCode === 0) {
    echo "✅ Link simbólico criado\n";
} else {
    echo "⚠️ Erro no link simbólico\n";
}

echo "\n🔧 ETAPA 9: OTIMIZANDO SISTEMA\n";

// Otimizar sistema
$optimizeCommands = [
    'config:cache' => 'Cache de configuração',
    'view:cache' => 'Cache de views',
    'route:clear' => 'Limpeza de rotas'
];

foreach ($optimizeCommands as $command => $description) {
    exec("{$phpPath} artisan {$command}", $output, $returnCode);
    if ($returnCode === 0) {
        echo "✅ {$description}\n";
    }
}

echo "\n🔧 ETAPA 10: CRIANDO ARQUIVO DE INSTALAÇÃO\n";

// Marcar como instalado
file_put_contents('storage/installed', 'INSTALLED_' . date('Y-m-d_H-i-s'));
echo "✅ Sistema marcado como instalado\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 NOVA INSTALAÇÃO SHOPPERZZ CRIADA COM SUCESSO!\n";
echo str_repeat("=", 60) . "\n";

echo "\n📋 INFORMAÇÕES DA NOVA INSTALAÇÃO:\n";
echo "📁 Diretório: {$newDir}\n";
echo "🌐 URL: http://localhost:8001\n";
echo "🗄️ Banco: shopperzz_novo\n";
echo "🇧🇷 Idioma: Português (único)\n";
echo "🔑 API Key: SHOPPERZZ-2025-FREE-LICENSE\n";

echo "\n🚀 COMO INICIAR:\n";
echo "1. Abra um novo terminal\n";
echo "2. Navegue para: cd \"{$newDir}\"\n";
echo "3. Execute: {$phpPath} -S localhost:8001 -t public\n";
echo "4. Acesse: http://localhost:8001\n";

echo "\n👤 LOGIN ADMIN:\n";
echo "Email: <EMAIL>\n";
echo "Senha: 123456\n";
echo "URL Admin: http://localhost:8001/admin\n";

echo "\n📱 PWA:\n";
echo "✅ Service Worker configurado\n";
echo "✅ Manifest.json criado\n";
echo "✅ Ícones PWA incluídos\n";
echo "✅ Instalável como app\n";

echo "\n🎯 RECURSOS INCLUÍDOS:\n";
echo "✅ Frontend completo\n";
echo "✅ Painel administrativo\n";
echo "✅ PWA (Progressive Web App)\n";
echo "✅ Sistema POS\n";
echo "✅ WhatsApp Ordering\n";
echo "✅ Gestão de inventário\n";
echo "✅ Apenas em português\n";
echo "✅ Totalmente otimizado\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 INSTALAÇÃO COMPLETA FINALIZADA!\n";
echo "📅 " . date('d/m/Y H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";
?>
