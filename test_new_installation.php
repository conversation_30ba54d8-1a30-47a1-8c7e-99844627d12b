<?php
/**
 * Teste da nova instalação Shopperzz
 */

echo "=== TESTE DA NOVA INSTALAÇÃO SHOPPERZZ ===\n\n";

$baseUrl = 'http://localhost:8001';
$apiKey = 'SHOPPERZZ-2025-FREE-LICENSE';

echo "🌐 URL Base: {$baseUrl}\n";
echo "🔑 API Key: {$apiKey}\n\n";

// Função para fazer requisições
function makeRequest($url, $headers = []) {
    $ch = curl_init();
    curl_setopt($ch, CURLOPT_URL, $url);
    curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
    curl_setopt($ch, CURLOPT_TIMEOUT, 10);
    curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
    curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    
    curl_close($ch);
    
    return [
        'code' => $httpCode,
        'response' => $response,
        'error' => $error
    ];
}

$tests = [];

// Teste 1: Página inicial
echo "🧪 TESTE 1: PÁGINA INICIAL\n";
$result = makeRequest($baseUrl);
if ($result['code'] == 200) {
    echo "✅ Frontend funcionando (HTTP {$result['code']})\n";
    $tests['frontend'] = true;
} else {
    echo "❌ Frontend com problema (HTTP {$result['code']})\n";
    if ($result['error']) echo "   Erro: {$result['error']}\n";
    $tests['frontend'] = false;
}

// Teste 2: Página admin
echo "\n🧪 TESTE 2: PAINEL ADMINISTRATIVO\n";
$result = makeRequest($baseUrl . '/admin');
if ($result['code'] == 200 || $result['code'] == 302) {
    echo "✅ Admin funcionando (HTTP {$result['code']})\n";
    $tests['admin'] = true;
} else {
    echo "❌ Admin com problema (HTTP {$result['code']})\n";
    $tests['admin'] = false;
}

// Teste 3: API de idiomas
echo "\n🧪 TESTE 3: API DE IDIOMAS\n";
$headers = [
    'x-api-key: ' . $apiKey,
    'Accept: application/json'
];
$result = makeRequest($baseUrl . '/api/frontend/language', $headers);
if ($result['code'] == 200) {
    echo "✅ API de idiomas funcionando (HTTP {$result['code']})\n";
    
    $data = json_decode($result['response'], true);
    if ($data && isset($data['data'])) {
        echo "📋 Idiomas encontrados:\n";
        foreach ($data['data'] as $lang) {
            $status = $lang['status'] == 5 ? 'ATIVO' : 'INATIVO';
            echo "   • {$lang['name']} ({$lang['code']}) - {$status}\n";
        }
    }
    $tests['api_languages'] = true;
} else {
    echo "❌ API de idiomas com problema (HTTP {$result['code']})\n";
    $tests['api_languages'] = false;
}

// Teste 4: API do português específico
echo "\n🧪 TESTE 4: API DO PORTUGUÊS\n";
$result = makeRequest($baseUrl . '/api/frontend/language/show/4', $headers);
if ($result['code'] == 200) {
    echo "✅ API do português funcionando (HTTP {$result['code']})\n";
    
    $data = json_decode($result['response'], true);
    if ($data && isset($data['data'])) {
        $lang = $data['data'];
        echo "🇧🇷 Português encontrado:\n";
        echo "   • ID: {$lang['id']}\n";
        echo "   • Nome: {$lang['name']}\n";
        echo "   • Código: {$lang['code']}\n";
        echo "   • Status: " . ($lang['status'] == 5 ? 'ATIVO' : 'INATIVO') . "\n";
    }
    $tests['api_portuguese'] = true;
} else {
    echo "❌ API do português com problema (HTTP {$result['code']})\n";
    $tests['api_portuguese'] = false;
}

// Teste 5: PWA Manifest
echo "\n🧪 TESTE 5: PWA MANIFEST\n";
$result = makeRequest($baseUrl . '/manifest.json');
if ($result['code'] == 200) {
    echo "✅ PWA Manifest funcionando (HTTP {$result['code']})\n";
    
    $manifest = json_decode($result['response'], true);
    if ($manifest && isset($manifest['name'])) {
        echo "📱 PWA configurado:\n";
        echo "   • Nome: {$manifest['name']}\n";
        echo "   • Descrição: " . ($manifest['short_name'] ?? 'N/A') . "\n";
    }
    $tests['pwa_manifest'] = true;
} else {
    echo "❌ PWA Manifest com problema (HTTP {$result['code']})\n";
    $tests['pwa_manifest'] = false;
}

// Teste 6: Service Worker
echo "\n🧪 TESTE 6: SERVICE WORKER\n";
$result = makeRequest($baseUrl . '/sw.js');
if ($result['code'] == 200) {
    echo "✅ Service Worker funcionando (HTTP {$result['code']})\n";
    $tests['service_worker'] = true;
} else {
    echo "❌ Service Worker com problema (HTTP {$result['code']})\n";
    $tests['service_worker'] = false;
}

// Teste 7: Assets (CSS/JS)
echo "\n🧪 TESTE 7: ASSETS ESTÁTICOS\n";
$assets = [
    '/css/app.css' => 'CSS Principal',
    '/js/app.js' => 'JavaScript Principal',
    '/images/default/logo.png' => 'Logo Padrão'
];

$assetsOk = 0;
foreach ($assets as $asset => $description) {
    $result = makeRequest($baseUrl . $asset);
    if ($result['code'] == 200) {
        echo "✅ {$description} (HTTP {$result['code']})\n";
        $assetsOk++;
    } else {
        echo "⚠️ {$description} (HTTP {$result['code']})\n";
    }
}
$tests['assets'] = $assetsOk > 0;

// Relatório Final
echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 RELATÓRIO FINAL DOS TESTES\n";
echo str_repeat("=", 60) . "\n";

$totalTests = count($tests);
$passedTests = array_sum($tests);

echo "\n✅ TESTES APROVADOS: {$passedTests}/{$totalTests}\n\n";

foreach ($tests as $test => $passed) {
    $status = $passed ? '✅ PASSOU' : '❌ FALHOU';
    $testName = strtoupper(str_replace('_', ' ', $test));
    echo "   {$testName}: {$status}\n";
}

echo "\n🎯 RESULTADO GERAL:\n";
if ($passedTests == $totalTests) {
    echo "🎉 TODOS OS TESTES PASSARAM!\n";
    echo "✅ Sistema totalmente funcional\n";
} elseif ($passedTests >= $totalTests * 0.8) {
    echo "✅ Sistema funcionando bem (80%+ dos testes)\n";
    echo "⚠️ Alguns recursos podem precisar de ajustes\n";
} else {
    echo "⚠️ Sistema parcialmente funcional\n";
    echo "🔧 Requer correções adicionais\n";
}

echo "\n📋 INFORMAÇÕES DA INSTALAÇÃO:\n";
echo "🌐 Frontend: {$baseUrl}\n";
echo "👤 Admin: {$baseUrl}/admin\n";
echo "📧 Login: <EMAIL>\n";
echo "🔑 Senha: 123456\n";
echo "🇧🇷 Idioma: Português (único)\n";
echo "📱 PWA: Instalável pelo navegador\n";

echo "\n🚀 COMO USAR:\n";
echo "1. Acesse: {$baseUrl}\n";
echo "2. Para admin: {$baseUrl}/admin\n";
echo "3. Para instalar PWA: Clique no ícone de instalação do navegador\n";

echo "\n📱 RECURSOS PWA:\n";
echo "✅ Funciona offline\n";
echo "✅ Instalável como app\n";
echo "✅ Notificações push\n";
echo "✅ Interface nativa\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 TESTE COMPLETO FINALIZADO!\n";
echo "📅 " . date('d/m/Y H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";
?>
