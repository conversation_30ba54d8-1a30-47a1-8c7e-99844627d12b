<?php
/**
 * Script para habilitar/desabilitar o instalador do Shopperzz
 * 
 * Uso:
 * php toggle_installer.php disable  - Desabilita o instalador (cria arquivo installed)
 * php toggle_installer.php enable   - Habilita o instalador (remove arquivo installed)
 * php toggle_installer.php status   - Mostra o status atual
 */

$action = $argv[1] ?? 'status';
$installedFile = __DIR__ . '/storage/installed';

echo "=== CONTROLADOR DO INSTALADOR SHOPPERZZ ===\n\n";

switch ($action) {
    case 'disable':
        if (file_exists($installedFile)) {
            echo "ℹ️  O instalador já está desabilitado.\n";
        } else {
            $content = "Shopperzz installer successfully INSTALLED on " . date('Y-m-d h:i:s A') . "\n";
            $content .= "Installation completed via toggle script.\n";
            $content .= "System is now ready for production use.\n";
            
            file_put_contents($installedFile, $content);
            echo "✅ Instalador DESABILITADO com sucesso!\n";
            echo "📁 Arquivo 'storage/installed' criado.\n";
            echo "🌐 Agora você pode acessar: http://localhost:8000\n";
        }
        break;
        
    case 'enable':
        if (file_exists($installedFile)) {
            unlink($installedFile);
            echo "✅ Instalador HABILITADO com sucesso!\n";
            echo "🗑️  Arquivo 'storage/installed' removido.\n";
            echo "🔧 Agora você pode acessar: http://localhost:8000/install\n";
        } else {
            echo "ℹ️  O instalador já está habilitado.\n";
        }
        break;
        
    case 'status':
    default:
        if (file_exists($installedFile)) {
            echo "📊 STATUS: INSTALADOR DESABILITADO\n";
            echo "✅ Sistema pronto para uso normal\n";
            echo "📁 Arquivo: storage/installed existe\n";
            echo "🌐 Acesse: http://localhost:8000\n";
            echo "\n📄 Conteúdo do arquivo:\n";
            echo "   " . str_replace("\n", "\n   ", file_get_contents($installedFile));
        } else {
            echo "📊 STATUS: INSTALADOR HABILITADO\n";
            echo "🔧 Sistema em modo de instalação\n";
            echo "📁 Arquivo: storage/installed NÃO existe\n";
            echo "🌐 Acesse: http://localhost:8000/install\n";
        }
        break;
}

echo "\n=== COMANDOS DISPONÍVEIS ===\n";
echo "php toggle_installer.php disable  - Desabilitar instalador\n";
echo "php toggle_installer.php enable   - Habilitar instalador\n";
echo "php toggle_installer.php status   - Ver status atual\n";

echo "\n=== INFORMAÇÕES DE LOGIN (após instalação) ===\n";
echo "👤 Email: <EMAIL>\n";
echo "🔑 Senha: 123456\n";

echo "\n=== FIM ===\n";
?>
