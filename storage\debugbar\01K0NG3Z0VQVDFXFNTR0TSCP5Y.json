{"__meta": {"id": "01K0NG3Z0VQVDFXFNTR0TSCP5Y", "datetime": "2025-07-21 09:23:27", "utime": **********.133095, "method": "GET", "uri": "/api/frontend/product-category/tree", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.76331, "end": **********.133118, "duration": 0.3698079586029053, "duration_str": "370ms", "measures": [{"label": "Booting", "start": **********.76331, "relative_start": 0, "end": **********.982681, "relative_end": **********.982681, "duration": 0.*****************, "duration_str": "219ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.982694, "relative_start": 0.*****************, "end": **********.133121, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.006939, "relative_start": 0.*****************, "end": **********.014681, "relative_end": **********.014681, "duration": 0.007742166519165039, "duration_str": "7.74ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.124751, "relative_start": 0.*****************, "end": **********.129326, "relative_end": **********.129326, "duration": 0.004575014114379883, "duration_str": "4.58ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.12936, "relative_start": 0.*****************, "end": **********.129392, "relative_end": **********.129392, "duration": 3.1948089599609375e-05, "duration_str": "32μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "36MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.055200000000000006, "accumulated_duration_str": "55.2ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "with recursive `laravel_cte` as ((select `product_categories`.*, 0 as `depth`, cast(`id` as char(65535)) as `path` from `product_categories` where `parent_id` is null) union all (select `product_categories`.*, `depth` + 1 as `depth`, concat(`path`, '.', `product_categories`.`id`) from `product_categories` inner join `laravel_cte` on `laravel_cte`.`id` = `product_categories`.`parent_id`)) select * from `laravel_cte` where `status` = 5", "type": "query", "params": [], "bindings": [".", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/staudenmeir/laravel-adjacency-list/src/Eloquent/Traits/BuildsAdjacencyListQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-adjacency-list\\src\\Eloquent\\Traits\\BuildsAdjacencyListQueries.php", "line": 26}, {"index": 15, "namespace": null, "name": "app/Services/ProductCategoryService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\ProductCategoryService.php", "line": 63}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/ProductCategoryController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\ProductCategoryController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.061307, "duration": 0.055200000000000006, "duration_str": "55.2ms", "memory": 0, "memory_str": null, "filename": "BuildsAdjacencyListQueries.php:26", "source": {"index": 13, "namespace": null, "name": "vendor/staudenmeir/laravel-adjacency-list/src/Eloquent/Traits/BuildsAdjacencyListQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-adjacency-list\\src\\Eloquent\\Traits\\BuildsAdjacencyListQueries.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fstaudenmeir%2Flaravel-adjacency-list%2Fsrc%2FEloquent%2FTraits%2FBuildsAdjacencyListQueries.php&line=26", "ajax": false, "filename": "BuildsAdjacencyListQueries.php", "line": "26"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product-category/tree", "action_name": "frontend.product-category.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@tree", "uri": "GET api/frontend/product-category/tree", "controller": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@tree<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductCategoryController.php&line=38\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/product-category", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductCategoryController.php&line=38\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ProductCategoryController.php:38-45</a>", "middleware": "api, installed, apiKey, localization", "duration": "485ms", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1928983564 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1928983564\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1549960003 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1549960003\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1137182248 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IndJT05aeElic2ZOTWtZUGdCQmdKd1E9PSIsInZhbHVlIjoiU2xSblFKbzNRQ2VDQ3NKK1NlUzhlZm5CRUEvb2xaTGphK1kyZGxEcDg2M0VhanoyRTFmcjBNK3dtSXZkMHhucUJxeXpXd3pJeUZZcE5mL2krMVhuOURnTmFvMVh5R25XaGxST3pJWHA2OVRGdzdSOWI2MXc1eTV1VFBwS0FRTVciLCJtYWMiOiI5MzdlMjliZTAxNzU1M2E5MjlhMDYyMDZiZjI0YzY5OTBkNjNjOGM4ZjYzNDFjZTBiNjhjYTA4MDgyNjhhZmFmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6IndJT05aeElic2ZOTWtZUGdCQmdKd1E9PSIsInZhbHVlIjoiU2xSblFKbzNRQ2VDQ3NKK1NlUzhlZm5CRUEvb2xaTGphK1kyZGxEcDg2M0VhanoyRTFmcjBNK3dtSXZkMHhucUJxeXpXd3pJeUZZcE5mL2krMVhuOURnTmFvMVh5R25XaGxST3pJWHA2OVRGdzdSOWI2MXc1eTV1VFBwS0FRTVciLCJtYWMiOiI5MzdlMjliZTAxNzU1M2E5MjlhMDYyMDZiZjI0YzY5OTBkNjNjOGM4ZjYzNDFjZTBiNjhjYTA4MDgyNjhhZmFmIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6Im1sWExMU1Q3VmdUNVVKYlZGV3VLTHc9PSIsInZhbHVlIjoiZk5xNlpwS0J5Y0ovOGhOVmJJNlVYVDcza080TFZBM2MyT00zbC9ESTdLRWlZb0RreFJ2SGUvRmh6ZUNOb3lYRFZGVjZJM0haQUtNL1VSVUZhR3E3OXRuNklBNUJXakRuUEpITnRnM0lLN0I0WU9ac3d0ZTFLeVhJUFkraGF6N3IiLCJtYWMiOiJiMDhkNmZlN2QxNTYyMzVlYjMzNzAwMWRiNzUzMjQ2MjBkMTlhNzFjNjEyMDk3MTk3MTIzM2RkZDFhODY5OTE3IiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1137182248\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IndJT05aeElic2ZOTWtZUGdCQmdKd1E9PSIsInZhbHVlIjoiU2xSblFKbzNRQ2VDQ3NKK1NlUzhlZm5CRUEvb2xaTGphK1kyZGxEcDg2M0VhanoyRTFmcjBNK3dtSXZkMHhucUJxeXpXd3pJeUZZcE5mL2krMVhuOURnTmFvMVh5R25XaGxST3pJWHA2OVRGdzdSOWI2MXc1eTV1VFBwS0FRTVciLCJtYWMiOiI5MzdlMjliZTAxNzU1M2E5MjlhMDYyMDZiZjI0YzY5OTBkNjNjOGM4ZjYzNDFjZTBiNjhjYTA4MDgyNjhhZmFmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Im1sWExMU1Q3VmdUNVVKYlZGV3VLTHc9PSIsInZhbHVlIjoiZk5xNlpwS0J5Y0ovOGhOVmJJNlVYVDcza080TFZBM2MyT00zbC9ESTdLRWlZb0RreFJ2SGUvRmh6ZUNOb3lYRFZGVjZJM0haQUtNL1VSVUZhR3E3OXRuNklBNUJXakRuUEpITnRnM0lLN0I0WU9ac3d0ZTFLeVhJUFkraGF6N3IiLCJtYWMiOiJiMDhkNmZlN2QxNTYyMzVlYjMzNzAwMWRiNzUzMjQ2MjBkMTlhNzFjNjEyMDk3MTk3MTIzM2RkZDFhODY5OTE3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1875196724 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:23:27 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1875196724\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2067533163 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2067533163\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product-category/tree", "action_name": "frontend.product-category.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@tree"}, "badge": null}}