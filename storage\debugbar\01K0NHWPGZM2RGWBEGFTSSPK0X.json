{"__meta": {"id": "01K0NHWPGZM2RGWBEGFTSSPK0X", "datetime": "2025-07-21 09:54:26", "utime": **********.208456, "method": "GET", "uri": "/api/frontend/product-category?paginate=0&order_column=id&order_type=asc&status=5", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753070065.941265, "end": **********.208472, "duration": 0.26720690727233887, "duration_str": "267ms", "measures": [{"label": "Booting", "start": 1753070065.941265, "relative_start": 0, "end": **********.157857, "relative_end": **********.157857, "duration": 0.*****************, "duration_str": "217ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.157871, "relative_start": 0.*****************, "end": **********.208473, "relative_end": 9.5367431640625e-07, "duration": 0.050601959228515625, "duration_str": "50.6ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.182508, "relative_start": 0.****************, "end": **********.189798, "relative_end": **********.189798, "duration": 0.0072901248931884766, "duration_str": "7.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.205472, "relative_start": 0.****************, "end": **********.206004, "relative_end": **********.206004, "duration": 0.0005319118499755859, "duration_str": "532μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "400 Bad Request", "full_url": "http://localhost:8000/api/frontend/product-category?order_column=id&order_type=asc&paginate=0&status...", "action_name": "frontend.product-category.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@index", "uri": "GET api/frontend/product-category", "controller": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductCategoryController.php&line=48\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/product-category", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductCategoryController.php&line=48\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ProductCategoryController.php:48-55</a>", "middleware": "api, installed, apiKey, localization", "duration": "399ms", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1961457432 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>paginate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>order_column</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order_type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1961457432\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-589392517 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-589392517\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1161194825 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">pt</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"781 characters\">XSRF-TOKEN=eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6InoybndEdnhHdnpHdVFCbjdaSzZDTlE9PSIsInZhbHVlIjoiZ3g0SXFkNU02dW9HYzh6VUYrbVgzVEVpeEpkTWMzbEZuVjdKQW1QVHFLRzJ5OWVYUUMxekk4aUtoVDFKaDBoR094eFF3VmJXMFY1VVZMUEVnWklrTXlKWkkwaWhCcmhueCtXODRkOXRxS05PaTRKMzZqQml2RTkrZ0tLTy9GdXgiLCJtYWMiOiJlYjdiNjlkOThmZTdjYWEwNzk5N2Y3NTcxMTgwNzJjNjQ1ZmJmMDQzMmJkMjcxZDIwYTJiYWE3NDViNWE4ZjBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1161194825\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-105362707 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InoybndEdnhHdnpHdVFCbjdaSzZDTlE9PSIsInZhbHVlIjoiZ3g0SXFkNU02dW9HYzh6VUYrbVgzVEVpeEpkTWMzbEZuVjdKQW1QVHFLRzJ5OWVYUUMxekk4aUtoVDFKaDBoR094eFF3VmJXMFY1VVZMUEVnWklrTXlKWkkwaWhCcmhueCtXODRkOXRxS05PaTRKMzZqQml2RTkrZ0tLTy9GdXgiLCJtYWMiOiJlYjdiNjlkOThmZTdjYWEwNzk5N2Y3NTcxMTgwNzJjNjQ1ZmJmMDQzMmJkMjcxZDIwYTJiYWE3NDViNWE4ZjBhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-105362707\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-551108547 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:54:26 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-551108547\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1095756374 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1095756374\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "400 Bad Request", "full_url": "http://localhost:8000/api/frontend/product-category?order_column=id&order_type=asc&paginate=0&status...", "action_name": "frontend.product-category.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@index"}, "badge": "400 Bad Request"}}