{"__meta": {"id": "01K0NGH804CTGA7000KXN1NEMG", "datetime": "2025-07-21 09:30:42", "utime": **********.308949, "method": "GET", "uri": "/api/frontend/product/popular-products?paginate=0&rand=8", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753068641.968411, "end": **********.308963, "duration": 0.34055209159851074, "duration_str": "341ms", "measures": [{"label": "Booting", "start": 1753068641.968411, "relative_start": 0, "end": **********.186585, "relative_end": **********.186585, "duration": 0.*****************, "duration_str": "218ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.186599, "relative_start": 0.*****************, "end": **********.308965, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.211261, "relative_start": 0.*****************, "end": **********.218205, "relative_end": **********.218205, "duration": 0.006943941116333008, "duration_str": "6.94ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.302659, "relative_start": 0.*****************, "end": **********.306731, "relative_end": **********.306731, "duration": 0.004071950912475586, "duration_str": "4.07ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.306761, "relative_start": 0.****************, "end": **********.306782, "relative_end": **********.306782, "duration": 2.09808349609375e-05, "duration_str": "21μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02574, "accumulated_duration_str": "25.74ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `products`.`id`, `products`.`name`, `products`.`sku`, `products`.`slug`, `products`.`selling_price`, `products`.`variation_price`, `products`.`add_to_flash_sale`, `products`.`offer_start_date`, `products`.`offer_end_date`, `products`.`discount`, `products`.`status`, (select sum(star) from `product_reviews` where `product_id` = `products`.`id`) as `rating_star`, (select count(product_id) from `product_reviews` where `product_id` = `products`.`id`) as `rating_star_count`, (select count(*) from `stocks` where `products`.`id` = `stocks`.`product_id`) as `order_countable_count` from `products` where (`status` = 5) and `products`.`deleted_at` is null order by `order_countable_count` desc, RAND() limit 8", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ProductService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\ProductService.php", "line": 345}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/ProductController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\ProductController.php", "line": 56}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.2704442, "duration": 0.02574, "duration_str": "25.74ms", "memory": 0, "memory_str": null, "filename": "ProductService.php:345", "source": {"index": 15, "namespace": null, "name": "app/Services/ProductService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\ProductService.php", "line": 345}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FServices%2FProductService.php&line=345", "ajax": false, "filename": "ProductService.php", "line": "345"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product/popular-products?paginate=0&rand=8", "action_name": "frontend.product.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductController@mostPopularProducts", "uri": "GET api/frontend/product/popular-products", "controller": "App\\Http\\Controllers\\Frontend\\ProductController@mostPopularProducts<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductController.php&line=53\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/product", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductController.php&line=53\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ProductController.php:53-60</a>", "middleware": "api, installed, apiKey, localization", "duration": "456ms", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1323028566 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>paginate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>rand</span>\" => \"<span class=sf-dump-str>8</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1323028566\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-146942200 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-146942200\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-582578947 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImlzelkrYkpML0FtQTV1Vms5dExKUWc9PSIsInZhbHVlIjoiN3ByNXJZRmlGWEpYVjFSYkpHRnhYdDVmMXMwRlp6eG9nd3hkTzNyTHBtQ1pWVkNSNFJibWhDQVdtL1hqWFJ4cU01Y2RMeFA5Rm5KRXRwK20ySHZOaXJFeVdldUVHRk84NEM3VWIvL0dPZyswOENtUEdQZ1pzSkNlc2ZUNnF4czQiLCJtYWMiOiIyY2JlYTViNmE2YTkyOWMzZGFiNzA3OGNhZjYwNDdjMThlNjlhMzMxZDcxZGEwNGQ5ODE5ODQ3OWIwNzYwNmZmIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6ImlzelkrYkpML0FtQTV1Vms5dExKUWc9PSIsInZhbHVlIjoiN3ByNXJZRmlGWEpYVjFSYkpHRnhYdDVmMXMwRlp6eG9nd3hkTzNyTHBtQ1pWVkNSNFJibWhDQVdtL1hqWFJ4cU01Y2RMeFA5Rm5KRXRwK20ySHZOaXJFeVdldUVHRk84NEM3VWIvL0dPZyswOENtUEdQZ1pzSkNlc2ZUNnF4czQiLCJtYWMiOiIyY2JlYTViNmE2YTkyOWMzZGFiNzA3OGNhZjYwNDdjMThlNjlhMzMxZDcxZGEwNGQ5ODE5ODQ3OWIwNzYwNmZmIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6InZhOE03T3hkbDh2dTRGWVgxblVHM1E9PSIsInZhbHVlIjoiZ1BmKzM1emNETTJXTFAvZWRkeFRjUDh6UFVoUjA5Z1JzZGIzRzAxWTU1ZDVDOTA3QXA1MkVTaVQ3VzVhYzVwNlRXOEdvdEpieXk4eEhKSmNrWU9SQXd3YnYySkFnR0U0MzBJTDFEcWxVZDNpMXFWQUJDVjNXRjRxRkFVN1dDTDUiLCJtYWMiOiI2MjQ1YWZhZWFiYWEwYzIxMjhiYTI5ZjU4YTI4ZTBmMjY0NTVjZGJkYTQ1NTgwNDFmNjI4YjUzZWY5OTIyNjFiIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582578947\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-9359994 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImlzelkrYkpML0FtQTV1Vms5dExKUWc9PSIsInZhbHVlIjoiN3ByNXJZRmlGWEpYVjFSYkpHRnhYdDVmMXMwRlp6eG9nd3hkTzNyTHBtQ1pWVkNSNFJibWhDQVdtL1hqWFJ4cU01Y2RMeFA5Rm5KRXRwK20ySHZOaXJFeVdldUVHRk84NEM3VWIvL0dPZyswOENtUEdQZ1pzSkNlc2ZUNnF4czQiLCJtYWMiOiIyY2JlYTViNmE2YTkyOWMzZGFiNzA3OGNhZjYwNDdjMThlNjlhMzMxZDcxZGEwNGQ5ODE5ODQ3OWIwNzYwNmZmIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InZhOE03T3hkbDh2dTRGWVgxblVHM1E9PSIsInZhbHVlIjoiZ1BmKzM1emNETTJXTFAvZWRkeFRjUDh6UFVoUjA5Z1JzZGIzRzAxWTU1ZDVDOTA3QXA1MkVTaVQ3VzVhYzVwNlRXOEdvdEpieXk4eEhKSmNrWU9SQXd3YnYySkFnR0U0MzBJTDFEcWxVZDNpMXFWQUJDVjNXRjRxRkFVN1dDTDUiLCJtYWMiOiI2MjQ1YWZhZWFiYWEwYzIxMjhiYTI5ZjU4YTI4ZTBmMjY0NTVjZGJkYTQ1NTgwNDFmNjI4YjUzZWY5OTIyNjFiIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-9359994\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1046118150 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:30:42 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1046118150\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1937638848 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1937638848\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product/popular-products?paginate=0&rand=8", "action_name": "frontend.product.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductController@mostPopularProducts"}, "badge": null}}