{"__meta": {"id": "01K0NDF9YKWD12G8AQKKZ3GSB1", "datetime": "2025-07-21 08:37:13", "utime": **********.043957, "method": "GET", "uri": "/api/admin/country-code", "ip": "127.0.0.1"}, "messages": {"count": 37, "messages": [{"message": "[08:37:12] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 457", "message_html": null, "is_string": false, "label": "warning", "time": **********.737194, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetGet($key) should either be compatible with ArrayAccess::offsetGet(mixed $offset): mixed, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 468", "message_html": null, "is_string": false, "label": "warning", "time": **********.737574, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetSet($key, $value) should either be compatible with ArrayAccess::offsetSet(mixed $offset, mixed $value): void, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 484", "message_html": null, "is_string": false, "label": "warning", "time": **********.737833, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetUnset($key) should either be compatible with ArrayAccess::offsetUnset(mixed $offset): void, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 499", "message_html": null, "is_string": false, "label": "warning", "time": **********.738083, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::count() should either be compatible with Countable::count(): int, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 593", "message_html": null, "is_string": false, "label": "warning", "time": **********.738333, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::getIterator() should either be compatible with IteratorAggregate::getIterator(): Traversable, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 605", "message_html": null, "is_string": false, "label": "warning", "time": **********.738584, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::jsonSerialize() should either be compatible with JsonSerializable::jsonSerialize(): mixed, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 579", "message_html": null, "is_string": false, "label": "warning", "time": **********.73884, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.829039, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.832565, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.835424, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.855587, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.881102, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.886798, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.941604, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.944534, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.976914, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.983677, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:12] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.998777, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.026594, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.027154, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.027709, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.028258, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.028799, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.029349, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.029917, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.030596, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.031226, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.031841, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.032396, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.032919, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.033436, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.033943, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.034447, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.034958, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.035459, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.035957, "xdebug_link": null, "collector": "log"}, {"message": "[08:37:13] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.036467, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.304027, "end": **********.044015, "duration": 0.739987850189209, "duration_str": "740ms", "measures": [{"label": "Booting", "start": **********.304027, "relative_start": 0, "end": **********.647854, "relative_end": **********.647854, "duration": 0.****************, "duration_str": "344ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.647866, "relative_start": 0.****************, "end": **********.044018, "relative_end": 3.0994415283203125e-06, "duration": 0.****************, "duration_str": "396ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.67028, "relative_start": 0.****************, "end": **********.675486, "relative_end": **********.675486, "duration": 0.005206108093261719, "duration_str": "5.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.038409, "relative_start": 0.***************, "end": **********.041701, "relative_end": **********.041701, "duration": 0.003292083740234375, "duration_str": "3.29ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.041728, "relative_start": 0.****************, "end": **********.041748, "relative_end": **********.041748, "duration": 2.002716064453125e-05, "duration_str": "20μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "74MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.25", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00532, "accumulated_duration_str": "5.32ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.696883, "duration": 0.0032, "duration_str": "3.2ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 60.15}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.715738, "duration": 0.00046, "duration_str": "460μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "kesung_loja", "explain": null, "start_percent": 60.15, "width_percent": 8.647}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-21 08:37:12', `personal_access_tokens`.`updated_at` = '2025-07-21 08:37:12' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-07-21 08:37:12", "2025-07-21 08:37:12", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/staudenmeir/laravel-cte/src/Query/Traits/BuildsExpressionQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-cte\\src\\Query\\Traits\\BuildsExpressionQueries.php", "line": 225}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.7225099, "duration": 0.00166, "duration_str": "1.66ms", "memory": 0, "memory_str": null, "filename": "BuildsExpressionQueries.php:225", "source": {"index": 10, "namespace": null, "name": "vendor/staudenmeir/laravel-cte/src/Query/Traits/BuildsExpressionQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-cte\\src\\Query\\Traits\\BuildsExpressionQueries.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fstaudenmeir%2Flaravel-cte%2Fsrc%2FQuery%2FTraits%2FBuildsExpressionQueries.php&line=225", "ajax": false, "filename": "BuildsExpressionQueries.php", "line": "225"}, "connection": "kesung_loja", "explain": null, "start_percent": 68.797, "width_percent": 31.203}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost/api/admin/country-code", "action_name": "admin.country-code.", "controller_action": "App\\Http\\Controllers\\Admin\\CountryCodeController@index", "uri": "GET api/admin/country-code", "controller": "App\\Http\\Controllers\\Admin\\CountryCodeController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FCountryCodeController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/admin/country-code", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FCountryCodeController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CountryCodeController.php:19-26</a>", "middleware": "api, auth:sanctum", "duration": "735ms", "peak_memory": "78MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1713605371 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1713605371\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-202479191 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-202479191\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-844677011 data-indent-pad=\"  \"><span class=sf-dump-note>array:14</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">localhost</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"22 characters\">http://localhost:8000/</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">same-site</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-844677011\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1487059367 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1487059367\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1518202128 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 02:37:13 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1518202128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1532377252 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1532377252\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost/api/admin/country-code", "action_name": "admin.country-code.", "controller_action": "App\\Http\\Controllers\\Admin\\CountryCodeController@index"}, "badge": null}}