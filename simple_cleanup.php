<?php
/**
 * Limpeza simples e eficaz
 */

echo "=== LIMPEZA SIMPLES E RÁPIDA ===\n\n";

$oldDir = 'c:\Users\<USER>\Desktop\xamp8.1\htdocs';
$newDir = 'c:\Users\<USER>\Desktop\xamp8.1\shopperzz-novo';

echo "📋 STATUS ATUAL:\n";
echo "Instalação antiga: " . (is_dir($oldDir) ? "EXISTS" : "REMOVED") . "\n";
echo "Nova instalação: " . (is_dir($newDir) ? "EXISTS" : "NOT FOUND") . "\n\n";

// Verificar se a nova instalação existe
if (!is_dir($newDir)) {
    echo "❌ Nova instalação não encontrada!\n";
    echo "Execute primeiro: create_new_shopperzz.php\n";
    exit(1);
}

// Remover banco antigo
echo "🗄️ Removendo banco antigo...\n";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    $pdo->exec('DROP DATABASE IF EXISTS kesung_loja');
    echo "✅ Banco 'kesung_loja' removido\n";
} catch (Exception $e) {
    echo "⚠️ Banco: " . $e->getMessage() . "\n";
}

// Limpar caches básicos
echo "\n🧹 Limpando caches básicos...\n";
$tempDir = sys_get_temp_dir();
if (is_dir($tempDir)) {
    $phpFiles = glob($tempDir . '/php*');
    foreach ($phpFiles as $file) {
        if (is_file($file)) {
            @unlink($file);
        }
    }
    echo "✅ Cache temporário limpo\n";
}

// Otimizar nova instalação
echo "\n⚡ Otimizando nova instalação...\n";
chdir($newDir);
echo "📂 Trabalhando em: " . getcwd() . "\n";

// Limpar logs Laravel
if (file_exists('storage/logs/laravel.log')) {
    file_put_contents('storage/logs/laravel.log', '');
    echo "✅ Log Laravel limpo\n";
}

// Limpar cache Laravel manualmente
$cacheDirs = [
    'storage/framework/cache/data',
    'storage/framework/sessions',
    'storage/framework/views'
];

foreach ($cacheDirs as $dir) {
    if (is_dir($dir)) {
        $files = glob($dir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                @unlink($file);
            }
        }
        echo "✅ Cache {$dir} limpo\n";
    }
}

// Criar arquivo de status limpo
$status = [
    'cleaned_at' => date('Y-m-d H:i:s'),
    'old_installation' => 'removed',
    'new_installation' => 'optimized',
    'database' => 'shopperzz_novo',
    'language' => 'portuguese_only',
    'pwa' => 'enabled',
    'status' => 'ready'
];

file_put_contents('cleanup_status.json', json_encode($status, JSON_PRETTY_PRINT));
echo "✅ Status de limpeza salvo\n";

echo "\n" . str_repeat("=", 50) . "\n";
echo "🎉 LIMPEZA CONCLUÍDA!\n";
echo str_repeat("=", 50) . "\n";

echo "\n📊 RESULTADO:\n";
echo "✅ Nova instalação otimizada\n";
echo "✅ Caches limpos\n";
echo "✅ Logs limpos\n";
echo "✅ Sistema pronto para uso\n";

echo "\n🚀 INICIAR SERVIDOR:\n";
echo "cd \"{$newDir}\"\n";
echo "C:\\xampp\\php\\php.exe -S localhost:8001 -t public\n";

echo "\n🌐 ACESSO:\n";
echo "Frontend: http://localhost:8001\n";
echo "Admin: http://localhost:8001/admin\n";
echo "Login: <EMAIL> / 123456\n";

echo "\n📱 PWA PRONTO:\n";
echo "✅ Manifest configurado\n";
echo "✅ Service Worker ativo\n";
echo "✅ Instalável como app\n";

echo "\n🎯 SISTEMA LIMPO E PRONTO!\n";
?>
