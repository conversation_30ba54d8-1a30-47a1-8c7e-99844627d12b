<?php
/**
 * Script para verificar o status da instalação do Shopperzz
 * Execute este arquivo para verificar se a instalação está completa
 */

echo "=== VERIFICADOR DE INSTALAÇÃO SHOPPERZZ ===\n\n";

// Verifica se o arquivo 'installed' existe
$installedFile = __DIR__ . '/storage/installed';

if (file_exists($installedFile)) {
    echo "✅ STATUS: INSTALAÇÃO COMPLETA\n";
    echo "📁 Arquivo: storage/installed encontrado\n";
    echo "📅 Conteúdo:\n";
    echo "   " . file_get_contents($installedFile) . "\n";
    echo "\n🎉 O sistema está pronto para uso!\n";
    echo "🌐 Acesse: http://localhost:8000\n";
    echo "👤 Login padrão: <EMAIL>\n";
    echo "🔑 Senha padrão: 123456\n";
} else {
    echo "❌ STATUS: INSTALAÇÃO PENDENTE\n";
    echo "📁 Arquivo: storage/installed NÃO encontrado\n";
    echo "\n📋 Para completar a instalação:\n";
    echo "1. Acesse: http://localhost:8000/install\n";
    echo "2. Complete todos os passos do instalador\n";
    echo "3. OU execute este comando para criar o arquivo manualmente:\n";
    echo "   touch storage/installed\n";
}

echo "\n=== INFORMAÇÕES ADICIONAIS ===\n";
echo "📂 Diretório atual: " . __DIR__ . "\n";
echo "🐘 Versão PHP: " . PHP_VERSION . "\n";
echo "⏰ Data/Hora: " . date('Y-m-d H:i:s') . "\n";

// Verifica se o .env existe
$envFile = __DIR__ . '/.env';
if (file_exists($envFile)) {
    echo "✅ Arquivo .env encontrado\n";
} else {
    echo "❌ Arquivo .env NÃO encontrado\n";
}

// Verifica permissões da pasta storage
if (is_writable(__DIR__ . '/storage')) {
    echo "✅ Pasta storage tem permissão de escrita\n";
} else {
    echo "❌ Pasta storage NÃO tem permissão de escrita\n";
}

echo "\n=== FIM DA VERIFICAÇÃO ===\n";
?>
