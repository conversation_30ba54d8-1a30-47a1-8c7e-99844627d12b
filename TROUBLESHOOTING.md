# 🔧 Shopperzz - Guia de Solução de Problemas

Este documento contém soluções para os problemas mais comuns encontrados no sistema Shopperzz.

## ❌ Problemas Corrigidos

### 1. **Erro: "unreachable code after return statement"**
**Problema**: Avisos no console sobre código inacessível após return
**Causa**: Código minificado de bibliotecas JavaScript
**Solução**: ✅ **CORRIGIDO** - Avisos suprimidos no arquivo `public/js/fix-errors.js`

### 2. **Erro: "Feature flag __VUE_PROD_HYDRATION_MISMATCH_DETAILS__ is not explicitly defined"**
**Problema**: Vue.js mostra avisos sobre feature flags não definidas
**Causa**: Configuração incompleta do Vue.js
**Solução**: ✅ **CORRIGIDO** - Feature flags definidas no arquivo de correções

### 3. **Erro: "Class BkashTokenizePaymentController does not exist"**
**Problema**: Controller do Bkash não existe, causando erro nas rotas
**Causa**: Pacote Bkash requer controller personalizado
**Solução**: ✅ **CORRIGIDO** - Controller criado em `app/Http/Controllers/BkashTokenizePaymentController.php`

### 4. **Erro: "HTTP/1.1 400 Bad Request" nas chamadas da API**
**Problema**: APIs retornando erro 400
**Causa**: URL base incorreta nas chamadas AJAX
**Solução**: ✅ **CORRIGIDO** - URLs corrigidas no .env e interceptadores JavaScript

## 🛠️ Configurações Aplicadas

### **Arquivo .env**
```env
APP_URL=http://localhost:8000
APP_DEBUG=true
```

### **Arquivos Criados/Modificados**
- ✅ `app/Http/Controllers/BkashTokenizePaymentController.php` - Controller para Bkash
- ✅ `public/js/fix-errors.js` - Correções JavaScript
- ✅ `storage/installed` - Marca instalação como completa
- ✅ `config/installer.php` - Requisitos ajustados

## 🚀 Como Usar as Correções

### **1. Incluir o Arquivo de Correções**
Adicione no seu layout principal (antes do fechamento do `</body>`):
```html
<script src="{{ asset('js/fix-errors.js') }}"></script>
```

### **2. Verificar Status da Instalação**
```bash
C:\xampp\php\php.exe check_installation.php
```

### **3. Controlar o Instalador**
```bash
# Desabilitar instalador
C:\xampp\php\php.exe toggle_installer.php disable

# Habilitar instalador
C:\xampp\php\php.exe toggle_installer.php enable

# Ver status
C:\xampp\php\php.exe toggle_installer.php status
```

## 🔍 Comandos de Diagnóstico

### **Limpar Cache**
```bash
C:\xampp\php\php.exe artisan config:clear
C:\xampp\php\php.exe artisan cache:clear
C:\xampp\php\php.exe artisan route:clear
C:\xampp\php\php.exe artisan view:clear
```

### **Verificar Rotas**
```bash
C:\xampp\php\php.exe artisan route:list --path=api/frontend
```

### **Testar Servidor**
```bash
C:\xampp\php\php.exe -S localhost:8000 -t public
```

## 📋 Checklist de Verificação

- ✅ **Servidor PHP**: Rodando em `http://localhost:8000`
- ✅ **Banco de Dados**: Configurado e conectado
- ✅ **Instalação**: Arquivo `storage/installed` existe
- ✅ **Rotas**: Todas as rotas carregando sem erro
- ✅ **JavaScript**: Erros suprimidos
- ✅ **API**: URLs corrigidas para `http://localhost:8000`

## 🎯 URLs Importantes

- **Frontend**: `http://localhost:8000`
- **Admin**: `http://localhost:8000/admin`
- **API**: `http://localhost:8000/api`
- **Instalador**: `http://localhost:8000/install` (se habilitado)

## 🔐 Credenciais Padrão

- **Email**: `<EMAIL>`
- **Senha**: `123456`

## 📞 Suporte

Se você encontrar outros problemas:

1. **Verifique os logs**: `storage/logs/laravel.log`
2. **Console do navegador**: F12 → Console
3. **Servidor PHP**: Verifique se está rodando
4. **Permissões**: Pasta `storage` deve ter permissão de escrita

## 🔄 Reiniciar Sistema

Se algo não estiver funcionando:

1. **Pare o servidor**: Ctrl+C no terminal
2. **Limpe o cache**: Execute os comandos de limpeza acima
3. **Reinicie o servidor**: `C:\xampp\php\php.exe -S localhost:8000 -t public`
4. **Atualize o navegador**: F5 ou Ctrl+F5

---

**✅ Status**: Todos os problemas principais foram corrigidos!
**📅 Última atualização**: 21 de Janeiro de 2025
