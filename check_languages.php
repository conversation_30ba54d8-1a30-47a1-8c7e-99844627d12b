<?php
require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== VERIFICAÇÃO DE IDIOMAS ===\n\n";

try {
    $languages = App\Models\Language::all();
    
    if ($languages->count() > 0) {
        echo "📋 Idiomas encontrados no banco de dados:\n\n";
        foreach ($languages as $lang) {
            $status = $lang->status == 1 ? 'ATIVO' : 'INATIVO';
            echo "ID: {$lang->id} | Nome: {$lang->name} | Código: {$lang->code} | Status: {$status}\n";
        }
        
        echo "\n=== TESTE DE API ===\n";
        echo "Para testar a API do idioma português (ID: 4), use:\n";
        echo "curl -H 'x-api-key: SHOPPERZZ-2025-FREE-LICENSE' -H 'Accept: application/json' http://localhost:8000/api/frontend/language/show/4\n";
        
    } else {
        echo "❌ Nenhum idioma encontrado no banco de dados.\n";
        echo "Execute o seeder: php artisan db:seed --class=LanguageTableSeeder\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erro ao consultar idiomas: " . $e->getMessage() . "\n";
}

echo "\n=== VERIFICAÇÃO DE CHAVE API ===\n";
$apiKey = env('MIX_API_KEY');
echo "Chave API configurada: {$apiKey}\n";

echo "\n=== TESTE DE CONECTIVIDADE ===\n";
echo "Testando conexão com o banco de dados...\n";

try {
    $connection = DB::connection();
    $pdo = $connection->getPdo();
    echo "✅ Conexão com banco de dados: OK\n";
    
    $result = DB::select('SELECT COUNT(*) as total FROM languages');
    echo "✅ Total de idiomas na tabela: " . $result[0]->total . "\n";
    
} catch (Exception $e) {
    echo "❌ Erro de conexão: " . $e->getMessage() . "\n";
}

echo "\n=== FIM DA VERIFICAÇÃO ===\n";
?>
