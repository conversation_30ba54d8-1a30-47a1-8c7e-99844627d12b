<?php
/**
 * SCRIPT COMPLETO DE LIMPEZA E CONFIGURAÇÃO
 * Remove instalação antiga e configura nova instalação limpa
 */

echo "=== LIMPEZA COMPLETA E NOVA CONFIGURAÇÃO ===\n\n";

$oldDir = 'c:\Users\<USER>\Desktop\xamp8.1\htdocs';
$newDir = 'c:\Users\<USER>\Desktop\xamp8.1\shopperzz-novo';
$phpPath = 'C:\xampp\php\php.exe';

echo "🗑️ ETAPA 1: REMOVENDO INSTALAÇÃO ANTIGA\n";
echo "📁 Diretório antigo: {$oldDir}\n";
echo "📁 Nova instalação: {$newDir}\n\n";

// Parar servidores antigos se estiverem rodando
echo "⏹️ Parando servidores antigos...\n";
exec('taskkill /F /IM php.exe 2>nul', $output, $returnCode);
if ($returnCode === 0) {
    echo "✅ Servidores PHP parados\n";
} else {
    echo "⚠️ Nenhum servidor PHP rodando\n";
}

// Aguardar um pouco para garantir que os processos pararam
sleep(2);

// Remover banco de dados antigo
echo "\n🗄️ Removendo banco de dados antigo...\n";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    $pdo->exec('DROP DATABASE IF EXISTS kesung_loja');
    echo "✅ Banco 'kesung_loja' removido\n";
} catch (Exception $e) {
    echo "⚠️ Erro ao remover banco antigo: " . $e->getMessage() . "\n";
}

// Remover diretório antigo
echo "\n📁 Removendo diretório antigo...\n";
if (is_dir($oldDir)) {
    echo "🗑️ Removendo: {$oldDir}\n";

    // Usar robocopy para limpar (método mais eficiente no Windows)
    $emptyDir = sys_get_temp_dir() . '\\empty_' . uniqid();
    mkdir($emptyDir);

    exec("robocopy \"{$emptyDir}\" \"{$oldDir}\" /MIR", $output, $returnCode);
    rmdir($emptyDir);

    // Remover o diretório vazio
    exec("rmdir /s /q \"{$oldDir}\"", $output, $returnCode);

    if (!is_dir($oldDir)) {
        echo "✅ Instalação antiga removida completamente\n";
    } else {
        echo "⚠️ Alguns arquivos podem ainda estar em uso\n";
    }
} else {
    echo "✅ Diretório antigo já não existe\n";
}

// Limpar caches do sistema
echo "\n🧹 ETAPA 2: LIMPANDO CACHES DO SISTEMA\n";

$cacheDirs = [
    'C:\\Windows\\Temp',
    sys_get_temp_dir(),
    'C:\\xampp\\tmp'
];

foreach ($cacheDirs as $cacheDir) {
    if (is_dir($cacheDir)) {
        echo "🧹 Limpando: {$cacheDir}\n";
        exec("del /q /s \"{$cacheDir}\\php*\" 2>nul");
        exec("del /q /s \"{$cacheDir}\\*cache*\" 2>nul");
    }
}

// Limpar logs do Apache/MySQL
echo "\n📋 Limpando logs...\n";
$logFiles = [
    'C:\\xampp\\apache\\logs\\access.log',
    'C:\\xampp\\apache\\logs\\error.log',
    'C:\\xampp\\mysql\\data\\*.log'
];

foreach ($logFiles as $logFile) {
    if (file_exists($logFile)) {
        file_put_contents($logFile, '');
        echo "✅ Log limpo: " . basename($logFile) . "\n";
    }
}

echo "✅ Limpeza do sistema concluída\n";

// Mudar para o diretório da nova instalação
chdir($newDir);
echo "\n📂 Trabalhando em: " . getcwd() . "\n\n";

try {
    // Criar banco de dados
    echo "🗄️ Criando banco de dados 'shopperzz_novo'...\n";
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    $pdo->exec('CREATE DATABASE IF NOT EXISTS shopperzz_novo CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci');
    echo "✅ Banco de dados criado com sucesso\n";

    // Importar dados do backup
    echo "📥 Importando dados do backup...\n";
    $backupFile = 'backup_original.sql';

    if (file_exists($backupFile)) {
        $importCmd = 'C:\xampp\mysql\bin\mysql -u root shopperzz_novo < ' . $backupFile;
        exec($importCmd, $output, $returnCode);

        if ($returnCode === 0) {
            echo "✅ Dados importados com sucesso\n";
        } else {
            echo "❌ Erro ao importar dados, executando migrações...\n";

            // Executar migrações se importação falhar
            if (file_exists('vendor/autoload.php')) {
                require 'vendor/autoload.php';
                $app = require_once 'bootstrap/app.php';
                $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

                // Executar migrações
                Artisan::call('migrate', ['--force' => true]);
                echo "✅ Migrações executadas\n";

                // Executar seeders
                Artisan::call('db:seed', ['--force' => true]);
                echo "✅ Seeders executados\n";
            }
        }
    } else {
        echo "⚠️ Arquivo de backup não encontrado, executando migrações...\n";

        // Executar migrações se não há backup
        if (file_exists('vendor/autoload.php')) {
            require 'vendor/autoload.php';
            $app = require_once 'bootstrap/app.php';
            $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

            // Executar migrações
            Artisan::call('migrate', ['--force' => true]);
            echo "✅ Migrações executadas\n";

            // Executar seeders
            Artisan::call('db:seed', ['--force' => true]);
            echo "✅ Seeders executados\n";
        }
    }

    // Configurar português como único idioma
    echo "🇧🇷 Configurando português como único idioma...\n";

    if (file_exists('vendor/autoload.php')) {
        require 'vendor/autoload.php';
        $app = require_once 'bootstrap/app.php';
        $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

        // Remover outros idiomas
        DB::table('languages')->whereNotIn('code', ['pt'])->delete();

        // Garantir que português existe e está ativo
        DB::table('languages')->updateOrInsert(
            ['code' => 'pt'],
            [
                'name' => 'Português',
                'code' => 'pt',
                'display_mode' => 1,
                'status' => 5,
                'created_at' => now(),
                'updated_at' => now(),
            ]
        );

        echo "✅ Português configurado como único idioma\n";

        // Verificar configuração final
        echo "\n📊 VERIFICAÇÃO FINAL:\n";
        $languages = DB::table('languages')->get();
        foreach ($languages as $lang) {
            $status = $lang->status == 5 ? 'ATIVO' : 'INATIVO';
            echo "   ID: {$lang->id} | {$lang->name} ({$lang->code}) - {$status}\n";
        }
    }

    echo "\n✅ BANCO DE DADOS CONFIGURADO COM SUCESSO!\n";

} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}

echo "\n🧹 ETAPA 3: LIMPEZA FINAL DA NOVA INSTALAÇÃO\n";

// Limpar caches da nova instalação
$newCacheDirs = [
    'storage/logs',
    'storage/framework/cache',
    'storage/framework/sessions',
    'storage/framework/views',
    'bootstrap/cache'
];

foreach ($newCacheDirs as $cacheDir) {
    if (is_dir($cacheDir)) {
        echo "🧹 Limpando cache: {$cacheDir}\n";

        // Limpar arquivos mas manter diretórios
        $files = glob($cacheDir . '/*');
        foreach ($files as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }

        // Limpar subdiretórios
        $subdirs = glob($cacheDir . '/*/*');
        foreach ($subdirs as $file) {
            if (is_file($file)) {
                unlink($file);
            }
        }

        echo "✅ Cache {$cacheDir} limpo\n";
    }
}

// Limpar logs específicos
$logFiles = [
    'storage/logs/laravel.log',
    'storage/logs/laravel-*.log'
];

foreach ($logFiles as $logPattern) {
    $files = glob($logPattern);
    foreach ($files as $file) {
        if (file_exists($file)) {
            file_put_contents($file, '');
            echo "✅ Log limpo: " . basename($file) . "\n";
        }
    }
}

// Otimizar nova instalação
echo "\n⚡ ETAPA 4: OTIMIZANDO NOVA INSTALAÇÃO\n";

if (file_exists('vendor/autoload.php')) {
    $optimizeCommands = [
        'config:clear' => 'Limpando cache de configuração',
        'cache:clear' => 'Limpando cache da aplicação',
        'view:clear' => 'Limpando cache de views',
        'route:clear' => 'Limpando cache de rotas',
        'config:cache' => 'Criando cache de configuração',
        'view:cache' => 'Criando cache de views'
    ];

    foreach ($optimizeCommands as $command => $description) {
        echo "🔄 {$description}...\n";
        exec("{$phpPath} artisan {$command}", $output, $returnCode);
        if ($returnCode === 0) {
            echo "✅ {$description} concluído\n";
        } else {
            echo "⚠️ Erro em: {$description}\n";
        }
    }
}

// Criar arquivo de status
echo "\n📄 Criando arquivo de status...\n";
$statusInfo = [
    'installation_date' => date('Y-m-d H:i:s'),
    'version' => '1.0.0',
    'language' => 'pt',
    'database' => 'shopperzz_novo',
    'url' => 'http://localhost:8001',
    'status' => 'clean_installation',
    'features' => [
        'frontend' => true,
        'admin' => true,
        'pwa' => true,
        'pos' => true,
        'whatsapp' => true,
        'inventory' => true,
        'portuguese_only' => true
    ]
];

file_put_contents('installation_status.json', json_encode($statusInfo, JSON_PRETTY_PRINT));
echo "✅ Status da instalação salvo\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 LIMPEZA E CONFIGURAÇÃO COMPLETAS!\n";
echo str_repeat("=", 60) . "\n";

echo "\n📊 RESUMO DA LIMPEZA:\n";
echo "✅ Instalação antiga removida completamente\n";
echo "✅ Banco de dados antigo removido\n";
echo "✅ Caches do sistema limpos\n";
echo "✅ Logs limpos\n";
echo "✅ Nova instalação otimizada\n";
echo "✅ Apenas português configurado\n";

echo "\n🚀 COMO INICIAR:\n";
echo "1. Servidor manual:\n";
echo "   cd \"{$newDir}\"\n";
echo "   {$phpPath} -S localhost:8001 -t public\n";
echo "\n2. Script automático:\n";
echo "   Clique duas vezes em: start_server.bat\n";

echo "\n🌐 ACESSO:\n";
echo "Frontend: http://localhost:8001\n";
echo "Admin: http://localhost:8001/admin\n";
echo "Login: <EMAIL> / 123456\n";

echo "\n📱 PWA:\n";
echo "✅ Manifest configurado\n";
echo "✅ Service Worker ativo\n";
echo "✅ Instalável como app\n";

echo "\n🎯 SISTEMA TOTALMENTE LIMPO E PRONTO!\n";
echo "📅 " . date('d/m/Y H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";
?>
