{"__meta": {"id": "01K0NJ3SNCC59F4N18XFKVM826", "datetime": "2025-07-21 09:58:18", "utime": **********.797827, "method": "GET", "uri": "/api/frontend/product-section", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.445502, "end": **********.797846, "duration": 0.3523440361022949, "duration_str": "352ms", "measures": [{"label": "Booting", "start": **********.445502, "relative_start": 0, "end": **********.71095, "relative_end": **********.71095, "duration": 0.*****************, "duration_str": "265ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.710964, "relative_start": 0.*****************, "end": **********.797849, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "86.88ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.73708, "relative_start": 0.****************, "end": **********.746441, "relative_end": **********.746441, "duration": 0.009360790252685547, "duration_str": "9.36ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.790219, "relative_start": 0.*****************, "end": **********.79508, "relative_end": **********.79508, "duration": 0.004860877990722656, "duration_str": "4.86ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.795118, "relative_start": 0.*****************, "end": **********.795142, "relative_end": **********.795142, "duration": 2.384185791015625e-05, "duration_str": "24μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "pt"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.004730000000000001, "accumulated_duration_str": "4.73ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `product_sections`.`id`, `product_sections`.`name`, `product_sections`.`slug`, `product_sections`.`status` from `product_sections` where `product_sections`.`status` = 5 order by `id` asc", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/ProductSectionService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\ProductSectionService.php", "line": 129}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/ProductSectionController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\ProductSectionController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.776549, "duration": 0.004730000000000001, "duration_str": "4.73ms", "memory": 0, "memory_str": null, "filename": "ProductSectionService.php:129", "source": {"index": 15, "namespace": null, "name": "app/Services/ProductSectionService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\ProductSectionService.php", "line": 129}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FServices%2FProductSectionService.php&line=129", "ajax": false, "filename": "ProductSectionService.php", "line": "129"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product-section", "action_name": "frontend.productSection.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductSectionController@index", "uri": "GET api/frontend/product-section", "controller": "App\\Http\\Controllers\\Frontend\\ProductSectionController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductSectionController.php&line=24\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/product-section", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductSectionController.php&line=24\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ProductSectionController.php:24-31</a>", "middleware": "api, installed, apiKey, localization", "duration": "462ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1742827528 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1742827528\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-2034512089 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2034512089\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1741530785 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 2|thT******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">pt</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IjRzUXZSMWFqSmdIWng1ci9BWE9xR3c9PSIsInZhbHVlIjoiUHIwaWNDRlFLR3N5RHZldHdmS2w3ZzcvdjBiZkFRMkZLTGJaWFRZbXhQREJxMHh0VkV0SGYrK1B4NlRxdzc4QytrNXg3SE94N3ZGd1hVYzd6SUp5TWpGWWc4OFpZdHdsNmFWWFdjMjJDZEhFdktTZ2IvMVVrc3pMZ20rRnU3aEYiLCJtYWMiOiJlNTUyNWM5NTRkODgxMjA0MDNhMzFkYTFhNDI2MzZmMjliNjAwMWI0MzM4YjBiMWQxNGFiNjViNDQ3ZmM5ZGVkIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1741530785\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1378219111 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRzUXZSMWFqSmdIWng1ci9BWE9xR3c9PSIsInZhbHVlIjoiUHIwaWNDRlFLR3N5RHZldHdmS2w3ZzcvdjBiZkFRMkZLTGJaWFRZbXhQREJxMHh0VkV0SGYrK1B4NlRxdzc4QytrNXg3SE94N3ZGd1hVYzd6SUp5TWpGWWc4OFpZdHdsNmFWWFdjMjJDZEhFdktTZ2IvMVVrc3pMZ20rRnU3aEYiLCJtYWMiOiJlNTUyNWM5NTRkODgxMjA0MDNhMzFkYTFhNDI2MzZmMjliNjAwMWI0MzM4YjBiMWQxNGFiNjViNDQ3ZmM5ZGVkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1378219111\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-799955444 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:58:18 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-799955444\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-98596232 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-98596232\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product-section", "action_name": "frontend.productSection.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductSectionController@index"}, "badge": null}}