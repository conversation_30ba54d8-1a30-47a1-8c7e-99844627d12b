{"__meta": {"id": "01K0NFMDAYES8S8SF246ATAZ7Z", "datetime": "2025-07-21 09:14:57", "utime": **********.503435, "method": "GET", "uri": "/api/frontend/setting", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.087417, "end": **********.503456, "duration": 0.41603922843933105, "duration_str": "416ms", "measures": [{"label": "Booting", "start": **********.087417, "relative_start": 0, "end": **********.313189, "relative_end": **********.313189, "duration": 0.*****************, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.313202, "relative_start": 0.****************, "end": **********.503458, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "190ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.338298, "relative_start": 0.****************, "end": **********.344764, "relative_end": **********.344764, "duration": 0.006465911865234375, "duration_str": "6.47ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.452303, "relative_start": 0.****************, "end": **********.500898, "relative_end": **********.500898, "duration": 0.*****************, "duration_str": "48.59ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.50093, "relative_start": 0.**************, "end": **********.500954, "relative_end": **********.500954, "duration": 2.384185791015625e-05, "duration_str": "24μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 15, "nb_statements": 15, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.032940000000000004, "accumulated_duration_str": "32.94ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `payload`, `key` from `settings` where `group` = 'company' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["company"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 12}], "start": **********.361465, "duration": 0.022670000000000003, "duration_str": "22.67ms", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 68.822}, {"sql": "select `payload`, `key` from `settings` where `group` = 'site' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["site"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 13}], "start": **********.3915012, "duration": 0.00082, "duration_str": "820μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 68.822, "width_percent": 2.489}, {"sql": "select `payload`, `key` from `settings` where `group` = 'shipping_setup' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["shipping_setup"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 14}], "start": **********.399043, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 71.311, "width_percent": 2.429}, {"sql": "select `payload`, `key` from `settings` where `group` = 'theme' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 15}], "start": **********.4065392, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 73.74, "width_percent": 2.429}, {"sql": "select `payload`, `key` from `settings` where `group` = 'otp' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["otp"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 16}], "start": **********.413981, "duration": 0.00086, "duration_str": "860μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 76.169, "width_percent": 2.611}, {"sql": "select `payload`, `key` from `settings` where `group` = 'social_media' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["social_media"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 17}], "start": **********.4218519, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 78.78, "width_percent": 2.368}, {"sql": "select `payload`, `key` from `settings` where `group` = 'notification' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["notification"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 18}], "start": **********.4295568, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 81.148, "width_percent": 2.307}, {"sql": "select `payload`, `key` from `settings` where `group` = 'whatsapp' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["whatsapp"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 19}], "start": **********.437132, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 83.455, "width_percent": 2.216}, {"sql": "select `payload`, `key` from `settings` where `group` = 'cookies' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["cookies"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 20}], "start": **********.4447498, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 85.671, "width_percent": 2.307}, {"sql": "select * from `settings` where (`key` = 'theme_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.460701, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 87.978, "width_percent": 1.943}, {"sql": "select * from `media` where `media`.`model_id` in (13) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.4699929, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 89.921, "width_percent": 2.155}, {"sql": "select * from `settings` where (`key` = 'theme_footer_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_footer_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.474808, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 92.077, "width_percent": 1.943}, {"sql": "select * from `media` where `media`.`model_id` in (15) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.481715, "duration": 0.0007, "duration_str": "700μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 94.019, "width_percent": 2.125}, {"sql": "select * from `settings` where (`key` = 'theme_favicon_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_favicon_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.4862828, "duration": 0.00058, "duration_str": "580μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 96.145, "width_percent": 1.761}, {"sql": "select * from `media` where `media`.`model_id` in (14) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.492856, "duration": 0.00069, "duration_str": "690μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 97.905, "width_percent": 2.095}]}, "models": {"data": {"App\\Models\\ThemeSetting": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FThemeSetting.php&line=1", "ajax": false, "filename": "ThemeSetting.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/setting", "action_name": "frontend.setting.", "controller_action": "App\\Http\\Controllers\\Frontend\\SettingController@index", "uri": "GET api/frontend/setting", "controller": "App\\Http\\Controllers\\Frontend\\SettingController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FSettingController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/setting", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FSettingController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/SettingController.php:20-27</a>", "middleware": "api, installed, apiKey, localization", "duration": "535ms", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-220573080 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-220573080\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-659960908 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-659960908\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-674689328 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVmekVKWE5LeE9LcnAvVm1tMzJSTkE9PSIsInZhbHVlIjoiQ09JSGdzUWRWQnhoWjNoZXZTYzk1NFhiQ011QTdDZ0pveDJ5QTFPZXZ6QXJ1VnRlZ2NUa1pOV0YxclhwbkFUdEVENkQ0Z3FCVElvUDRMOUlSL2s2UCtKNkJ3bkhJcC9zWVBYUElCVHhrRk12NEh4dkZiNmRtVzZ6MkpuNTRjRU8iLCJtYWMiOiIwNjg3YzBhOTJkN2YwMTE0NTk2NmQ5MzRjZDY1ZTUzMmEzNDk2YmE1N2NlYTM0NTYxMDNhY2RlNGE5NGJmMDExIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/admin/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6ImVmekVKWE5LeE9LcnAvVm1tMzJSTkE9PSIsInZhbHVlIjoiQ09JSGdzUWRWQnhoWjNoZXZTYzk1NFhiQ011QTdDZ0pveDJ5QTFPZXZ6QXJ1VnRlZ2NUa1pOV0YxclhwbkFUdEVENkQ0Z3FCVElvUDRMOUlSL2s2UCtKNkJ3bkhJcC9zWVBYUElCVHhrRk12NEh4dkZiNmRtVzZ6MkpuNTRjRU8iLCJtYWMiOiIwNjg3YzBhOTJkN2YwMTE0NTk2NmQ5MzRjZDY1ZTUzMmEzNDk2YmE1N2NlYTM0NTYxMDNhY2RlNGE5NGJmMDExIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6Ind4ZDNueEU5S21YcUYrRWpMRG1yK3c9PSIsInZhbHVlIjoiNUpvK0dlNEdIdDF5Q1dBK3Q0VFBTbmJTQWRCTStwazRFbWUzWlV6QW5OS3N1SGkySzY2UDZiWndrc1V6cmpaNzE3ZDBCSXg3TlpHblRmaE5rOURVS1BrMlQ3WTdtUFFiVEhTbmplTVpRVjI0TmE1aGNON1JNdkQza1FGR2M1V1kiLCJtYWMiOiJmNzQ2MDYyNWEzNWY3NTU5YmYwYmU1NDM2YjVlNDgyNzEzMWNkYzVjYTQwNWU1MGVkMjNiMDFkMGQzMTAwNzY4IiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-674689328\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-858379622 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImVmekVKWE5LeE9LcnAvVm1tMzJSTkE9PSIsInZhbHVlIjoiQ09JSGdzUWRWQnhoWjNoZXZTYzk1NFhiQ011QTdDZ0pveDJ5QTFPZXZ6QXJ1VnRlZ2NUa1pOV0YxclhwbkFUdEVENkQ0Z3FCVElvUDRMOUlSL2s2UCtKNkJ3bkhJcC9zWVBYUElCVHhrRk12NEh4dkZiNmRtVzZ6MkpuNTRjRU8iLCJtYWMiOiIwNjg3YzBhOTJkN2YwMTE0NTk2NmQ5MzRjZDY1ZTUzMmEzNDk2YmE1N2NlYTM0NTYxMDNhY2RlNGE5NGJmMDExIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ind4ZDNueEU5S21YcUYrRWpMRG1yK3c9PSIsInZhbHVlIjoiNUpvK0dlNEdIdDF5Q1dBK3Q0VFBTbmJTQWRCTStwazRFbWUzWlV6QW5OS3N1SGkySzY2UDZiWndrc1V6cmpaNzE3ZDBCSXg3TlpHblRmaE5rOURVS1BrMlQ3WTdtUFFiVEhTbmplTVpRVjI0TmE1aGNON1JNdkQza1FGR2M1V1kiLCJtYWMiOiJmNzQ2MDYyNWEzNWY3NTU5YmYwYmU1NDM2YjVlNDgyNzEzMWNkYzVjYTQwNWU1MGVkMjNiMDFkMGQzMTAwNzY4IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-858379622\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1274190028 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:14:57 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1274190028\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1214580501 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1214580501\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/setting", "action_name": "frontend.setting.", "controller_action": "App\\Http\\Controllers\\Frontend\\SettingController@index"}, "badge": null}}