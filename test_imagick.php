<?php
// Test script for Imagick extension
echo "<h2>Imagick Extension Test</h2>";

// Check if Imagick extension is loaded
if (extension_loaded('imagick')) {
    echo "<p style='color: green;'>✅ Imagick extension is loaded!</p>";
    
    try {
        // Test creating a simple image
        $image = new Imagick();
        $image->newImage(100, 100, new ImagickPixel('#ff0000'));
        $image->setImageFormat('png');
        
        echo "<p style='color: green;'>✅ Imagick is working correctly!</p>";
        echo "<p>Imagick version: " . Imagick::getVersion()['versionString'] . "</p>";
        
        // List supported formats
        $formats = Imagick::queryFormats();
        echo "<p>Supported formats: " . count($formats) . " formats</p>";
        echo "<details><summary>Show formats</summary>";
        echo "<p>" . implode(', ', array_slice($formats, 0, 20)) . "...</p>";
        echo "</details>";
        
    } catch (Exception $e) {
        echo "<p style='color: red;'>❌ Error testing Imagick: " . $e->getMessage() . "</p>";
    }
    
} else {
    echo "<p style='color: red;'>❌ Imagick extension is not loaded</p>";
    echo "<p>Please make sure you have:</p>";
    echo "<ul>";
    echo "<li>Downloaded php_imagick-3.7.0-8.1-ts-vs16-x64.zip</li>";
    echo "<li>Copied php_imagick.dll to C:\\xampp\\php\\ext\\</li>";
    echo "<li>Copied all other DLL files to C:\\xampp\\php\\</li>";
    echo "<li>Added 'extension=imagick' to php.ini</li>";
    echo "<li>Restarted the web server</li>";
    echo "</ul>";
}

// Show current PHP version and extensions
echo "<hr>";
echo "<h3>PHP Information</h3>";
echo "<p>PHP Version: " . PHP_VERSION . "</p>";
echo "<p>Thread Safety: " . (ZEND_THREAD_SAFE ? 'Enabled' : 'Disabled') . "</p>";
echo "<p>Architecture: " . (PHP_INT_SIZE === 8 ? 'x64' : 'x86') . "</p>";

echo "<h4>Currently Loaded Extensions:</h4>";
$extensions = get_loaded_extensions();
sort($extensions);
echo "<p>" . implode(', ', $extensions) . "</p>";
?>
