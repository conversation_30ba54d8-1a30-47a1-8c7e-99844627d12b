{"__meta": {"id": "01K0NHR6YBR7XCYX0XV2E1RVEZ", "datetime": "2025-07-21 09:51:59", "utime": **********.180536, "method": "GET", "uri": "/api/frontend/cookies", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753069918.896983, "end": **********.180547, "duration": 0.28356409072875977, "duration_str": "284ms", "measures": [{"label": "Booting", "start": 1753069918.896983, "relative_start": 0, "end": **********.134474, "relative_end": **********.134474, "duration": 0.*****************, "duration_str": "237ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.134484, "relative_start": 0.****************, "end": **********.180548, "relative_end": 9.5367431640625e-07, "duration": 0.046063899993896484, "duration_str": "46.06ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.156216, "relative_start": 0.****************, "end": **********.163549, "relative_end": **********.163549, "duration": 0.007333040237426758, "duration_str": "7.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.171307, "relative_start": 0.****************, "end": **********.178306, "relative_end": **********.178306, "duration": 0.006999015808105469, "duration_str": "7ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.178326, "relative_start": 0.****************, "end": **********.178349, "relative_end": **********.178349, "duration": 2.3126602172851562e-05, "duration_str": "23μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/cookies", "action_name": "frontend.cookies.", "controller_action": "App\\Http\\Controllers\\Frontend\\CookiesController@get", "uri": "GET api/frontend/cookies", "controller": "App\\Http\\Controllers\\Frontend\\CookiesController@get<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FCookiesController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/cookies", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FCookiesController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/CookiesController.php:21-28</a>", "middleware": "api, installed, apiKey, localization", "duration": "409ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1588445820 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1588445820\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1812183105 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1812183105\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1010956711 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IllYc3Y1SVBMSE9kQUE0eU80RXlaQ0E9PSIsInZhbHVlIjoiRWVqN0J0TzJIemhkbUJGYWFSbCtyZGtvcGVTWEhrUzVTblpGVnNJWEZQRmlEeVFwaU5tanNabjhLQVNHM2V4aEdRcDJFM3ZicU5odFFjajg3SkVCZWpYUE9Sc1g1ZjNyWWdJQXBhZWM0U0tFNk5NTFJXNHI0NCszZEFhRWxoVEYiLCJtYWMiOiJiYjE3NjAyMjg1ZmZkOTViM2QzMDAzYzU5Yjg3ODk0M2Y5YjhjYmYyMzFkOTYxZmI0OTJiNjc3YjIwN2VjZDQ0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6IllYc3Y1SVBMSE9kQUE0eU80RXlaQ0E9PSIsInZhbHVlIjoiRWVqN0J0TzJIemhkbUJGYWFSbCtyZGtvcGVTWEhrUzVTblpGVnNJWEZQRmlEeVFwaU5tanNabjhLQVNHM2V4aEdRcDJFM3ZicU5odFFjajg3SkVCZWpYUE9Sc1g1ZjNyWWdJQXBhZWM0U0tFNk5NTFJXNHI0NCszZEFhRWxoVEYiLCJtYWMiOiJiYjE3NjAyMjg1ZmZkOTViM2QzMDAzYzU5Yjg3ODk0M2Y5YjhjYmYyMzFkOTYxZmI0OTJiNjc3YjIwN2VjZDQ0IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IjJXSEFyZ0s4L0t5RHZGcTJhQ2cyY0E9PSIsInZhbHVlIjoiV0N0OHBHSkJmVllZcGRRMEZGNlQvRExoUDFCcjcwSnNBdVBtYjNwM3Rrb0l6RFNaUzhxdXFYQ1pIdjl4bFFrRUN1ZjIvaU5RN1FsNkVYYytRZGkrM24xM25CNlJPa3BkczR4dXZLL2NpckZkN0trUVE4TnY3VGw2dTZlOGpHNXAiLCJtYWMiOiJmNzBlMTgzMTM0OWYwMzczMDM3N2NlZWFkYjVlNDc1ZDQ3OWY0NDQ2MjhmZjM5NzY3MjQ1N2Q2ZWNlNThkYmE1IiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1010956711\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-2040384967 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IllYc3Y1SVBMSE9kQUE0eU80RXlaQ0E9PSIsInZhbHVlIjoiRWVqN0J0TzJIemhkbUJGYWFSbCtyZGtvcGVTWEhrUzVTblpGVnNJWEZQRmlEeVFwaU5tanNabjhLQVNHM2V4aEdRcDJFM3ZicU5odFFjajg3SkVCZWpYUE9Sc1g1ZjNyWWdJQXBhZWM0U0tFNk5NTFJXNHI0NCszZEFhRWxoVEYiLCJtYWMiOiJiYjE3NjAyMjg1ZmZkOTViM2QzMDAzYzU5Yjg3ODk0M2Y5YjhjYmYyMzFkOTYxZmI0OTJiNjc3YjIwN2VjZDQ0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjJXSEFyZ0s4L0t5RHZGcTJhQ2cyY0E9PSIsInZhbHVlIjoiV0N0OHBHSkJmVllZcGRRMEZGNlQvRExoUDFCcjcwSnNBdVBtYjNwM3Rrb0l6RFNaUzhxdXFYQ1pIdjl4bFFrRUN1ZjIvaU5RN1FsNkVYYytRZGkrM24xM25CNlJPa3BkczR4dXZLL2NpckZkN0trUVE4TnY3VGw2dTZlOGpHNXAiLCJtYWMiOiJmNzBlMTgzMTM0OWYwMzczMDM3N2NlZWFkYjVlNDc1ZDQ3OWY0NDQ2MjhmZjM5NzY3MjQ1N2Q2ZWNlNThkYmE1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2040384967\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2059745069 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:51:59 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2059745069\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-13545061 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-13545061\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/cookies", "action_name": "frontend.cookies.", "controller_action": "App\\Http\\Controllers\\Frontend\\CookiesController@get"}, "badge": null}}