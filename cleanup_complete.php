<?php
/**
 * Script de limpeza completa - Remove instalação antiga
 */

echo "=== LIMPEZA COMPLETA DO SISTEMA ===\n\n";

$oldDir = 'c:\Users\<USER>\Desktop\xamp8.1\htdocs';
$newDir = 'c:\Users\<USER>\Desktop\xamp8.1\shopperzz-novo';

echo "🗑️ REMOVENDO INSTALAÇÃO ANTIGA\n";
echo "📁 Diretório antigo: {$oldDir}\n";
echo "📁 Nova instalação: {$newDir}\n\n";

// 1. Parar todos os processos PHP
echo "⏹️ Parando processos PHP...\n";
exec('taskkill /F /IM php.exe /T 2>nul', $output, $returnCode);
sleep(3); // Aguardar processos pararem
echo "✅ Processos PHP parados\n";

// 2. Remover banco antigo
echo "\n🗄️ Removendo banco antigo...\n";
try {
    $pdo = new PDO('mysql:host=127.0.0.1;port=3306', 'root', '');
    $pdo->exec('DROP DATABASE IF EXISTS kesung_loja');
    echo "✅ Banco 'kesung_loja' removido\n";
} catch (Exception $e) {
    echo "⚠️ Banco antigo: " . $e->getMessage() . "\n";
}

// 3. Remover diretório antigo
echo "\n📁 Removendo diretório antigo...\n";
if (is_dir($oldDir)) {
    echo "🗑️ Removendo: {$oldDir}\n";
    
    // Método Windows para remoção forçada
    exec("rmdir /s /q \"{$oldDir}\" 2>nul", $output, $returnCode);
    
    // Verificar se foi removido
    if (!is_dir($oldDir)) {
        echo "✅ Instalação antiga removida\n";
    } else {
        echo "⚠️ Alguns arquivos podem estar em uso\n";
        
        // Tentar método alternativo
        exec("rd /s /q \"{$oldDir}\" 2>nul");
        
        if (!is_dir($oldDir)) {
            echo "✅ Instalação antiga removida (método alternativo)\n";
        } else {
            echo "⚠️ Diretório ainda existe, pode ter arquivos em uso\n";
        }
    }
} else {
    echo "✅ Diretório antigo já não existe\n";
}

// 4. Limpar caches do sistema
echo "\n🧹 LIMPANDO CACHES DO SISTEMA\n";

$tempDirs = [
    'C:\\Windows\\Temp',
    sys_get_temp_dir(),
    'C:\\xampp\\tmp'
];

foreach ($tempDirs as $tempDir) {
    if (is_dir($tempDir)) {
        echo "🧹 Limpando: {$tempDir}\n";
        exec("del /q /s \"{$tempDir}\\php*\" 2>nul");
        exec("del /q /s \"{$tempDir}\\*cache*\" 2>nul");
        exec("del /q /s \"{$tempDir}\\*tmp*\" 2>nul");
    }
}

// 5. Limpar logs
echo "\n📋 Limpando logs...\n";
$logFiles = [
    'C:\\xampp\\apache\\logs\\access.log',
    'C:\\xampp\\apache\\logs\\error.log'
];

foreach ($logFiles as $logFile) {
    if (file_exists($logFile)) {
        file_put_contents($logFile, '');
        echo "✅ Log limpo: " . basename($logFile) . "\n";
    }
}

// 6. Otimizar nova instalação
echo "\n⚡ OTIMIZANDO NOVA INSTALAÇÃO\n";
if (is_dir($newDir)) {
    chdir($newDir);
    echo "📂 Mudando para: " . getcwd() . "\n";
    
    // Limpar caches da nova instalação
    $cacheDirs = [
        'storage/logs',
        'storage/framework/cache/data',
        'storage/framework/sessions',
        'storage/framework/views',
        'bootstrap/cache'
    ];
    
    foreach ($cacheDirs as $cacheDir) {
        if (is_dir($cacheDir)) {
            echo "🧹 Limpando: {$cacheDir}\n";
            
            // Limpar arquivos
            $files = glob($cacheDir . '/*');
            foreach ($files as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
            
            // Limpar subdiretórios
            $subdirs = glob($cacheDir . '/*/*');
            foreach ($subdirs as $file) {
                if (is_file($file)) {
                    unlink($file);
                }
            }
        }
    }
    
    // Limpar logs Laravel
    if (file_exists('storage/logs/laravel.log')) {
        file_put_contents('storage/logs/laravel.log', '');
        echo "✅ Log Laravel limpo\n";
    }
    
    // Executar comandos Artisan se possível
    if (file_exists('vendor/autoload.php')) {
        echo "🔄 Executando comandos de otimização...\n";
        
        $commands = [
            'config:clear',
            'cache:clear',
            'view:clear',
            'route:clear'
        ];
        
        foreach ($commands as $command) {
            exec("C:\\xampp\\php\\php.exe artisan {$command} 2>nul", $output, $returnCode);
            if ($returnCode === 0) {
                echo "✅ {$command} executado\n";
            }
        }
    }
    
} else {
    echo "❌ Nova instalação não encontrada em: {$newDir}\n";
}

// 7. Criar resumo final
echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 RESUMO DA LIMPEZA COMPLETA\n";
echo str_repeat("=", 60) . "\n";

echo "\n✅ AÇÕES REALIZADAS:\n";
echo "🗑️ Instalação antiga removida\n";
echo "🗄️ Banco de dados antigo removido\n";
echo "🧹 Caches do sistema limpos\n";
echo "📋 Logs limpos\n";
echo "⚡ Nova instalação otimizada\n";

echo "\n🎯 RESULTADO:\n";
if (is_dir($newDir)) {
    echo "✅ Nova instalação disponível em: {$newDir}\n";
    echo "🌐 URL: http://localhost:8001\n";
    echo "👤 Admin: http://localhost:8001/admin\n";
    echo "📧 Login: <EMAIL> / 123456\n";
    echo "🇧🇷 Idioma: Português (único)\n";
    echo "📱 PWA: Totalmente configurado\n";
} else {
    echo "❌ Nova instalação não encontrada\n";
}

if (!is_dir($oldDir)) {
    echo "✅ Instalação antiga removida completamente\n";
} else {
    echo "⚠️ Instalação antiga ainda existe (alguns arquivos em uso)\n";
}

echo "\n🚀 PRÓXIMOS PASSOS:\n";
echo "1. Navegue para: cd \"{$newDir}\"\n";
echo "2. Inicie o servidor: C:\\xampp\\php\\php.exe -S localhost:8001 -t public\n";
echo "3. Ou use o script: start_server.bat\n";
echo "4. Acesse: http://localhost:8001\n";

echo "\n📱 PARA INSTALAR COMO PWA:\n";
echo "1. Abra http://localhost:8001 no navegador\n";
echo "2. Procure o ícone de 'Instalar' na barra de endereços\n";
echo "3. Clique em 'Instalar' para usar como app nativo\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 LIMPEZA COMPLETA FINALIZADA!\n";
echo "📅 " . date('d/m/Y H:i:s') . "\n";
echo "🎯 Sistema limpo e pronto para uso!\n";
echo str_repeat("=", 60) . "\n";
?>
