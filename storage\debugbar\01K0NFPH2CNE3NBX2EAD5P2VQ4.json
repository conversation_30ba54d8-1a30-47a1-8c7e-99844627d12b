{"__meta": {"id": "01K0NFPH2CNE3NBX2EAD5P2VQ4", "datetime": "2025-07-21 09:16:06", "utime": **********.861689, "method": "GET", "uri": "/api/frontend/cookies", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.555339, "end": **********.861704, "duration": 0.3063650131225586, "duration_str": "306ms", "measures": [{"label": "Booting", "start": **********.555339, "relative_start": 0, "end": **********.809067, "relative_end": **********.809067, "duration": 0.*****************, "duration_str": "254ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.809079, "relative_start": 0.****************, "end": **********.861706, "relative_end": 1.9073486328125e-06, "duration": 0.****************, "duration_str": "52.63ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.83402, "relative_start": 0.*****************, "end": **********.842839, "relative_end": **********.842839, "duration": 0.008819103240966797, "duration_str": "8.82ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.851849, "relative_start": 0.****************, "end": **********.85924, "relative_end": **********.85924, "duration": 0.0073909759521484375, "duration_str": "7.39ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.859263, "relative_start": 0.****************, "end": **********.859285, "relative_end": **********.859285, "duration": 2.2172927856445312e-05, "duration_str": "22μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "35MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 0, "nb_statements": 0, "nb_visible_statements": 0, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0, "accumulated_duration_str": "0μs", "memory_usage": 0, "memory_usage_str": null, "statements": []}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/cookies", "action_name": "frontend.cookies.", "controller_action": "App\\Http\\Controllers\\Frontend\\CookiesController@get", "uri": "GET api/frontend/cookies", "controller": "App\\Http\\Controllers\\Frontend\\CookiesController@get<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FCookiesController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/cookies", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FCookiesController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/CookiesController.php:21-28</a>", "middleware": "api, installed, apiKey, localization", "duration": "418ms", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1359428202 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1359428202\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1302065002 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1302065002\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1343831164 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImlGQ01ka2pNNWFnc1poSkdmOFBKdFE9PSIsInZhbHVlIjoiOHpQdHRDRkh5YWpRbm8wTXhYNkdqZmRpK2RtUHVhQWFncXNibzdUMzNFQkFFSlh3Smk0emowN0ZnZ1N5UDFOQVVjeG9GaG1HRDFOK0pnR05lcmRYb2ZPd3RsSDBsN0VEWlFyRkJBL3NidzVicVMxU0syL1pxUnJlQTVwdGtXcnIiLCJtYWMiOiIyODhlODFkZTE3ZThkY2FjNDhlODY1OTEyNjU2NDNhNjE5N2JhMGQwMmVjNDhlMDNhYmIzMjBlMzZlZmQzMDI5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6ImlGQ01ka2pNNWFnc1poSkdmOFBKdFE9PSIsInZhbHVlIjoiOHpQdHRDRkh5YWpRbm8wTXhYNkdqZmRpK2RtUHVhQWFncXNibzdUMzNFQkFFSlh3Smk0emowN0ZnZ1N5UDFOQVVjeG9GaG1HRDFOK0pnR05lcmRYb2ZPd3RsSDBsN0VEWlFyRkJBL3NidzVicVMxU0syL1pxUnJlQTVwdGtXcnIiLCJtYWMiOiIyODhlODFkZTE3ZThkY2FjNDhlODY1OTEyNjU2NDNhNjE5N2JhMGQwMmVjNDhlMDNhYmIzMjBlMzZlZmQzMDI5IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IjV5Vnh4UWorRmlrWXFjWFhqOGZSaGc9PSIsInZhbHVlIjoiSFNtS2dYTmVpU3AyVXE1YmFSK2YvOVErOHBkNFVPWm9keWtBUmFqanJQZUlPSFIzSWM0Y1I0aHJvSDlhaW94UEEzMEl4UG5ZRDlHZEpNWVZaNk82ZFFPRmJMSWJTdzU3MnkvMlBiVitMSUJ5M09VcVlVcG1BZklFSWtnOVlEY1YiLCJtYWMiOiJhYWQ5M2UxOGEwNzg2ZWQ1MjE2ODQ1ODVlMTE2ZGMyNmNlODM5YTY0MmYxNDRhNjRlNWZmODM2NjhhNzUzYTg2IiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1343831164\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-732805813 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6ImlGQ01ka2pNNWFnc1poSkdmOFBKdFE9PSIsInZhbHVlIjoiOHpQdHRDRkh5YWpRbm8wTXhYNkdqZmRpK2RtUHVhQWFncXNibzdUMzNFQkFFSlh3Smk0emowN0ZnZ1N5UDFOQVVjeG9GaG1HRDFOK0pnR05lcmRYb2ZPd3RsSDBsN0VEWlFyRkJBL3NidzVicVMxU0syL1pxUnJlQTVwdGtXcnIiLCJtYWMiOiIyODhlODFkZTE3ZThkY2FjNDhlODY1OTEyNjU2NDNhNjE5N2JhMGQwMmVjNDhlMDNhYmIzMjBlMzZlZmQzMDI5IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjV5Vnh4UWorRmlrWXFjWFhqOGZSaGc9PSIsInZhbHVlIjoiSFNtS2dYTmVpU3AyVXE1YmFSK2YvOVErOHBkNFVPWm9keWtBUmFqanJQZUlPSFIzSWM0Y1I0aHJvSDlhaW94UEEzMEl4UG5ZRDlHZEpNWVZaNk82ZFFPRmJMSWJTdzU3MnkvMlBiVitMSUJ5M09VcVlVcG1BZklFSWtnOVlEY1YiLCJtYWMiOiJhYWQ5M2UxOGEwNzg2ZWQ1MjE2ODQ1ODVlMTE2ZGMyNmNlODM5YTY0MmYxNDRhNjRlNWZmODM2NjhhNzUzYTg2IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-732805813\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-881792651 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:16:06 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-881792651\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1962404627 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1962404627\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/cookies", "action_name": "frontend.cookies.", "controller_action": "App\\Http\\Controllers\\Frontend\\CookiesController@get"}, "badge": null}}