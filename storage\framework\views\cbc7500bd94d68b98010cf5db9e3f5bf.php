<?php $__env->startSection('template_title'); ?>
    <?php echo e(trans('installer.site.templateTitle')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('title'); ?>
    <?php echo e(trans('installer.site.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('container'); ?>
    <ul class="installer-track">
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.index')); ?>')" class="done">
            <i class="fa-solid fa-house"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.requirement')); ?>')" class="done">
            <i class="fa-solid fa-server"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.permission')); ?>')" class="done">
            <i class="fa-sharp fa-solid fa-unlock"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.license')); ?>')" class="done">
            <i class="fa-solid fa-key"></i>
        </li>
        <li class="active"><i class="fa-solid fa-gear"></i></li>
        <li><i class="fa-solid fa-database"></i></li>
        <li><i class="fa-solid fa-unlock-keyhole"></i></li>
    </ul>

    <span class="my-6 w-full h-[1px] bg-[#EFF0F6]"></span>

    <form class="w-full" method="post" action="<?php echo e(route('installer.siteStore')); ?>">
        <input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">
        <div class="mb-4">
            <label class="text-sm font-medium block mb-1.5 text-heading">
                <?php echo e(trans('installer.site.label.app_name')); ?> <span class="text-[#E93C3C]">*</span>
            </label>
            <input name="app_name" type="text" value="<?php echo e(old('app_name')); ?>"
                class="w-full h-12 rounded-lg px-4 border border-[#D9DBE9]">
            <?php if($errors->has('app_name')): ?>
                <small class="block mt-2 text-sm font-medium text-[#E93C3C]"><?php echo e($errors->first('app_name')); ?></small>
            <?php endif; ?>
        </div>

        <div class="mb-8">
            <label class="text-sm font-medium block mb-1.5 text-heading">
                <?php echo e(trans('installer.site.label.app_url')); ?> <span class="text-[#E93C3C]">*</span>
            </label>
            <input name="app_url" type="text" value="<?php echo e(old('app_url')); ?>"
                class="w-full h-12 rounded-lg px-4 border border-[#D9DBE9]">
            <?php if($errors->has('app_url')): ?>
                <small class="block mt-2 text-sm font-medium text-[#E93C3C]"><?php echo e($errors->first('app_url')); ?></small>
            <?php endif; ?>
        </div>

        <button type="submit"
            class="w-fit mx-auto p-3 px-6 rounded-lg flex items-center justify-center gap-3 bg-primary text-white">
            <span class="text-sm font-medium capitalize"><?php echo e(trans('installer.site.next')); ?></span>
            <i class="fa-solid fa-angle-right text-sm"></i>
        </button>
    </form>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installer.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\resources\views\installer\site.blade.php ENDPATH**/ ?>