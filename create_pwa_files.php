<?php
/**
 * Criar arquivos PWA para a nova instalação
 */

echo "=== CRIANDO ARQUIVOS PWA ===\n\n";

$newDir = 'c:\Users\<USER>\Desktop\xamp8.1\shopperzz-novo';
chdir($newDir);

echo "📂 Trabalhando em: " . getcwd() . "\n\n";

// 1. Criar manifest.json
echo "📱 CRIANDO MANIFEST.JSON\n";
$manifest = [
    "name" => "Shopperzz - Nova Loja",
    "short_name" => "Shopperzz",
    "description" => "Sistema completo de e-commerce com POS e WhatsApp",
    "start_url" => "/",
    "display" => "standalone",
    "background_color" => "#ffffff",
    "theme_color" => "#007bff",
    "orientation" => "portrait-primary",
    "scope" => "/",
    "lang" => "pt-BR",
    "categories" => ["shopping", "business", "productivity"],
    "icons" => [
        [
            "src" => "/images/icons/icon-72x72.png",
            "sizes" => "72x72",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => "/images/icons/icon-96x96.png",
            "sizes" => "96x96",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => "/images/icons/icon-128x128.png",
            "sizes" => "128x128",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => "/images/icons/icon-144x144.png",
            "sizes" => "144x144",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => "/images/icons/icon-152x152.png",
            "sizes" => "152x152",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => "/images/icons/icon-192x192.png",
            "sizes" => "192x192",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => "/images/icons/icon-384x384.png",
            "sizes" => "384x384",
            "type" => "image/png",
            "purpose" => "maskable any"
        ],
        [
            "src" => "/images/icons/icon-512x512.png",
            "sizes" => "512x512",
            "type" => "image/png",
            "purpose" => "maskable any"
        ]
    ]
];

file_put_contents('public/manifest.json', json_encode($manifest, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES));
echo "✅ manifest.json criado\n";

// 2. Criar Service Worker
echo "\n🔧 CRIANDO SERVICE WORKER\n";
$serviceWorker = <<<JS
// Service Worker para Shopperzz PWA
const CACHE_NAME = 'shopperzz-v1.0.0';
const urlsToCache = [
    '/',
    '/css/app.css',
    '/js/app.js',
    '/images/default/logo.png',
    '/manifest.json'
];

// Instalação do Service Worker
self.addEventListener('install', function(event) {
    console.log('Service Worker: Instalando...');
    event.waitUntil(
        caches.open(CACHE_NAME)
            .then(function(cache) {
                console.log('Service Worker: Cache aberto');
                return cache.addAll(urlsToCache);
            })
    );
});

// Ativação do Service Worker
self.addEventListener('activate', function(event) {
    console.log('Service Worker: Ativando...');
    event.waitUntil(
        caches.keys().then(function(cacheNames) {
            return Promise.all(
                cacheNames.map(function(cacheName) {
                    if (cacheName !== CACHE_NAME) {
                        console.log('Service Worker: Removendo cache antigo:', cacheName);
                        return caches.delete(cacheName);
                    }
                })
            );
        })
    );
});

// Interceptar requisições
self.addEventListener('fetch', function(event) {
    event.respondWith(
        caches.match(event.request)
            .then(function(response) {
                // Cache hit - retorna resposta do cache
                if (response) {
                    return response;
                }

                return fetch(event.request).then(
                    function(response) {
                        // Verifica se recebemos uma resposta válida
                        if(!response || response.status !== 200 || response.type !== 'basic') {
                            return response;
                        }

                        // IMPORTANTE: Clone a resposta. Uma resposta é um stream
                        // e porque queremos que o navegador consuma a resposta
                        // assim como o cache consumindo a resposta, precisamos
                        // cloná-la para que tenhamos dois streams.
                        var responseToCache = response.clone();

                        caches.open(CACHE_NAME)
                            .then(function(cache) {
                                cache.put(event.request, responseToCache);
                            });

                        return response;
                    }
                );
            })
    );
});

// Notificações Push
self.addEventListener('push', function(event) {
    console.log('Service Worker: Push recebido');
    
    const options = {
        body: event.data ? event.data.text() : 'Nova notificação do Shopperzz!',
        icon: '/images/icons/icon-192x192.png',
        badge: '/images/icons/icon-72x72.png',
        vibrate: [100, 50, 100],
        data: {
            dateOfArrival: Date.now(),
            primaryKey: '2'
        },
        actions: [
            {
                action: 'explore',
                title: 'Ver detalhes',
                icon: '/images/icons/checkmark.png'
            },
            {
                action: 'close',
                title: 'Fechar',
                icon: '/images/icons/xmark.png'
            }
        ]
    };

    event.waitUntil(
        self.registration.showNotification('Shopperzz', options)
    );
});

// Clique em notificação
self.addEventListener('notificationclick', function(event) {
    console.log('Service Worker: Notificação clicada');
    event.notification.close();

    if (event.action === 'explore') {
        // Abrir a aplicação
        event.waitUntil(clients.openWindow('/'));
    } else if (event.action === 'close') {
        // Apenas fechar a notificação
        event.notification.close();
    } else {
        // Ação padrão - abrir a aplicação
        event.waitUntil(clients.openWindow('/'));
    }
});

console.log('Service Worker: Carregado e pronto!');
JS;

file_put_contents('public/sw.js', $serviceWorker);
echo "✅ sw.js criado\n";

// 3. Criar diretório de ícones
echo "\n🎨 CRIANDO ÍCONES PWA\n";
$iconsDir = 'public/images/icons';
if (!is_dir($iconsDir)) {
    mkdir($iconsDir, 0755, true);
    echo "✅ Diretório de ícones criado\n";
}

// Criar ícones simples (placeholder)
$iconSizes = [72, 96, 128, 144, 152, 192, 384, 512];
foreach ($iconSizes as $size) {
    $iconPath = "{$iconsDir}/icon-{$size}x{$size}.png";
    
    // Criar um ícone simples usando GD (se disponível)
    if (extension_loaded('gd')) {
        $image = imagecreate($size, $size);
        $bg = imagecolorallocate($image, 0, 123, 255); // Azul
        $text_color = imagecolorallocate($image, 255, 255, 255); // Branco
        
        // Adicionar texto "S" no centro
        $font_size = $size / 4;
        $text = 'S';
        $text_box = imagettfbbox($font_size, 0, __DIR__ . '/arial.ttf', $text);
        if (!$text_box) {
            // Se não tiver fonte TTF, usar fonte built-in
            imagestring($image, 5, $size/2 - 10, $size/2 - 10, $text, $text_color);
        }
        
        imagepng($image, $iconPath);
        imagedestroy($image);
        echo "✅ Ícone {$size}x{$size} criado\n";
    } else {
        // Se GD não estiver disponível, copiar um ícone existente ou criar placeholder
        $placeholder = "<?xml version='1.0' encoding='UTF-8'?>
<svg width='{$size}' height='{$size}' viewBox='0 0 {$size} {$size}' xmlns='http://www.w3.org/2000/svg'>
    <rect width='{$size}' height='{$size}' fill='#007bff'/>
    <text x='50%' y='50%' font-family='Arial' font-size='" . ($size/4) . "' fill='white' text-anchor='middle' dy='.3em'>S</text>
</svg>";
        file_put_contents(str_replace('.png', '.svg', $iconPath), $placeholder);
        echo "✅ Ícone SVG {$size}x{$size} criado\n";
    }
}

// 4. Criar arquivo de configuração PWA
echo "\n⚙️ CRIANDO CONFIGURAÇÃO PWA\n";
$pwaConfig = <<<JS
// Configuração PWA para Shopperzz
window.PWA_CONFIG = {
    name: 'Shopperzz - Nova Loja',
    version: '1.0.0',
    updateAvailable: false,
    
    // Registrar Service Worker
    registerServiceWorker: function() {
        if ('serviceWorker' in navigator) {
            window.addEventListener('load', function() {
                navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                        console.log('PWA: Service Worker registrado com sucesso:', registration.scope);
                        
                        // Verificar atualizações
                        registration.addEventListener('updatefound', function() {
                            console.log('PWA: Nova versão disponível');
                            window.PWA_CONFIG.updateAvailable = true;
                        });
                    })
                    .catch(function(error) {
                        console.log('PWA: Falha ao registrar Service Worker:', error);
                    });
            });
        }
    },
    
    // Solicitar permissão para notificações
    requestNotificationPermission: function() {
        if ('Notification' in window) {
            Notification.requestPermission().then(function(permission) {
                console.log('PWA: Permissão de notificação:', permission);
            });
        }
    },
    
    // Verificar se é PWA
    isPWA: function() {
        return window.matchMedia('(display-mode: standalone)').matches ||
               window.navigator.standalone === true;
    },
    
    // Mostrar prompt de instalação
    showInstallPrompt: function() {
        if (window.deferredPrompt) {
            window.deferredPrompt.prompt();
            window.deferredPrompt.userChoice.then(function(choiceResult) {
                console.log('PWA: Escolha do usuário:', choiceResult.outcome);
                window.deferredPrompt = null;
            });
        }
    }
};

// Auto-inicializar
document.addEventListener('DOMContentLoaded', function() {
    window.PWA_CONFIG.registerServiceWorker();
    
    // Capturar evento de instalação
    window.addEventListener('beforeinstallprompt', function(e) {
        e.preventDefault();
        window.deferredPrompt = e;
        console.log('PWA: Prompt de instalação capturado');
    });
    
    // Detectar quando foi instalado
    window.addEventListener('appinstalled', function(e) {
        console.log('PWA: Aplicativo instalado com sucesso');
        window.deferredPrompt = null;
    });
});

console.log('PWA: Configuração carregada');
JS;

file_put_contents('public/js/pwa-config.js', $pwaConfig);
echo "✅ pwa-config.js criado\n";

echo "\n📋 RESUMO DOS ARQUIVOS PWA CRIADOS:\n";
echo "✅ public/manifest.json - Manifesto da aplicação\n";
echo "✅ public/sw.js - Service Worker\n";
echo "✅ public/images/icons/ - Ícones PWA\n";
echo "✅ public/js/pwa-config.js - Configuração PWA\n";

echo "\n🔧 PARA ATIVAR O PWA:\n";
echo "1. Adicione no <head> do seu layout:\n";
echo "   <link rel=\"manifest\" href=\"/manifest.json\">\n";
echo "   <meta name=\"theme-color\" content=\"#007bff\">\n";
echo "\n2. Adicione antes do </body>:\n";
echo "   <script src=\"/js/pwa-config.js\"></script>\n";

echo "\n✅ ARQUIVOS PWA CRIADOS COM SUCESSO!\n";
?>
