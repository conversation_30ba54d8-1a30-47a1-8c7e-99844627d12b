<?php
/**
 * Script para testar a API do idioma português
 */

echo "=== TESTE DA API - IDIOMA PORTUGUÊS ===\n\n";

$apiKey = 'SHOPPERZZ-2025-FREE-LICENSE';
$url = 'http://localhost:8000/api/frontend/language/show/4';

echo "🔑 Chave API: {$apiKey}\n";
echo "🌐 URL: {$url}\n\n";

// Configurar contexto para a requisição
$context = stream_context_create([
    'http' => [
        'method' => 'GET',
        'header' => [
            'x-api-key: ' . $apiKey,
            'Accept: application/json',
            'Content-Type: application/json'
        ],
        'timeout' => 30
    ]
]);

echo "📡 Fazendo requisição...\n";

try {
    $response = file_get_contents($url, false, $context);
    
    if ($response === false) {
        echo "❌ Erro: Não foi possível fazer a requisição\n";
        
        // Verificar se há informações sobre o erro
        $error = error_get_last();
        if ($error) {
            echo "Detalhes do erro: " . $error['message'] . "\n";
        }
        
        // Verificar headers de resposta
        if (isset($http_response_header)) {
            echo "\n📋 Headers de resposta:\n";
            foreach ($http_response_header as $header) {
                echo "   {$header}\n";
            }
        }
    } else {
        echo "✅ Requisição bem-sucedida!\n\n";
        
        // Mostrar headers de resposta
        if (isset($http_response_header)) {
            echo "📋 Headers de resposta:\n";
            foreach ($http_response_header as $header) {
                echo "   {$header}\n";
            }
            echo "\n";
        }
        
        echo "📄 Resposta da API:\n";
        
        // Tentar decodificar JSON
        $data = json_decode($response, true);
        if ($data) {
            echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            
            if (isset($data['data'])) {
                echo "\n🇧🇷 INFORMAÇÕES DO IDIOMA PORTUGUÊS:\n";
                $lang = $data['data'];
                echo "   ID: " . ($lang['id'] ?? 'N/A') . "\n";
                echo "   Nome: " . ($lang['name'] ?? 'N/A') . "\n";
                echo "   Código: " . ($lang['code'] ?? 'N/A') . "\n";
                echo "   Status: " . ($lang['status'] == 1 ? 'ATIVO' : 'INATIVO') . "\n";
                echo "   Modo de Exibição: " . ($lang['display_mode'] == 1 ? 'LTR' : 'RTL') . "\n";
            }
        } else {
            echo $response . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Exceção: " . $e->getMessage() . "\n";
}

echo "\n=== TESTE ALTERNATIVO - LISTAR TODOS OS IDIOMAS ===\n";

$urlAll = 'http://localhost:8000/api/frontend/language';
echo "🌐 URL: {$urlAll}\n";

try {
    $contextAll = stream_context_create([
        'http' => [
            'method' => 'GET',
            'header' => [
                'x-api-key: ' . $apiKey,
                'Accept: application/json',
                'Content-Type: application/json'
            ],
            'timeout' => 30
        ]
    ]);
    
    $responseAll = file_get_contents($urlAll, false, $contextAll);
    
    if ($responseAll !== false) {
        echo "✅ Lista de idiomas obtida com sucesso!\n\n";
        
        $dataAll = json_decode($responseAll, true);
        if ($dataAll && isset($dataAll['data'])) {
            echo "📋 Idiomas disponíveis:\n";
            foreach ($dataAll['data'] as $lang) {
                $status = $lang['status'] == 1 ? 'ATIVO' : 'INATIVO';
                echo "   ID: {$lang['id']} | {$lang['name']} ({$lang['code']}) - {$status}\n";
            }
        }
    } else {
        echo "❌ Erro ao listar idiomas\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erro: " . $e->getMessage() . "\n";
}

echo "\n=== INSTRUÇÕES PARA USO NO NAVEGADOR ===\n";
echo "Para testar no navegador, você precisa:\n";
echo "1. Abrir as ferramentas de desenvolvedor (F12)\n";
echo "2. Ir na aba Console\n";
echo "3. Executar o seguinte código JavaScript:\n\n";

echo "fetch('http://localhost:8000/api/frontend/language/show/4', {\n";
echo "  method: 'GET',\n";
echo "  headers: {\n";
echo "    'x-api-key': 'SHOPPERZZ-2025-FREE-LICENSE',\n";
echo "    'Accept': 'application/json'\n";
echo "  }\n";
echo "})\n";
echo ".then(response => response.json())\n";
echo ".then(data => console.log('Idioma Português:', data))\n";
echo ".catch(error => console.error('Erro:', error));\n";

echo "\n=== FIM DO TESTE ===\n";
?>
