<?php $__env->startSection('template_title'); ?>
    <?php echo e(trans('installer.license.templateTitle')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('title'); ?>
    <?php echo e(trans('installer.license.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('container'); ?>
    <ul class="installer-track">
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.index')); ?>')" class="done">
            <i class="fa-solid fa-house"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.requirement')); ?>')" class="done">
            <i class="fa-solid fa-server"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.permission')); ?>')" class="done">
            <i class="fa-sharp fa-solid fa-unlock"></i>
        </li>
        <li class="active"><i class="fa-solid fa-key"></i></li>
        <li><i class="fa-solid fa-gear"></i></li>
        <li><i class="fa-solid fa-database"></i></li>
        <li><i class="fa-solid fa-unlock-keyhole"></i></li>
    </ul>

    <span class="my-6 w-full h-[1px] bg-[#EFF0F6]"></span>

    <form method="post" action="<?php echo e(route('installer.licenseStore')); ?>" class="w-full">
        <input type="hidden" name="_token" value="<?php echo e(csrf_token()); ?>">
        <!-- Informações sobre licenças disponíveis -->
        <div class="mb-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h3 class="text-lg font-semibold text-blue-800 mb-3">📄 Licenças Disponíveis para Shopperzz</h3>
            <div class="space-y-2 text-sm text-blue-700">
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                    <code class="bg-white px-2 py-1 rounded border">SHOPPERZZ-2025-FREE-LICENSE</code>
                    <span class="text-gray-600">- Licença gratuita para uso pessoal</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                    <code class="bg-white px-2 py-1 rounded border">SHOPPERZZ-DEMO-LICENSE-2025</code>
                    <span class="text-gray-600">- Licença para demonstração</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                    <code class="bg-white px-2 py-1 rounded border">SHOPPERZZ-DEV-LICENSE-FREE</code>
                    <span class="text-gray-600">- Licença para desenvolvimento</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                    <code class="bg-white px-2 py-1 rounded border">FREE-SHOPPERZZ-LICENSE-2025</code>
                    <span class="text-gray-600">- Licença gratuita completa</span>
                </div>
                <div class="flex items-center gap-2">
                    <span class="w-2 h-2 bg-green-500 rounded-full"></span>
                    <code class="bg-white px-2 py-1 rounded border">SHOPPERZZ-UNLIMITED-LICENSE</code>
                    <span class="text-gray-600">- Licença ilimitada</span>
                </div>
            </div>
            <p class="mt-3 text-xs text-blue-600">💡 <strong>Dica:</strong> Copie e cole qualquer uma das licenças acima no campo abaixo.</p>
        </div>

        <div class="mb-4">
            <label class="text-sm font-medium block mb-1.5 text-heading">
                <?php echo e(trans('installer.license.label.license_code')); ?> <span class="text-[#E93C3C]">*</span>
                <span class="text-gray-500 text-xs">(Use uma das licenças listadas acima)</span>
            </label>
            <input name="license_key" type="text" value="<?php echo e(old('license_key')); ?>"
                placeholder="Ex: SHOPPERZZ-2025-FREE-LICENSE"
                class="w-full h-12 rounded-lg px-4 border border-[#D9DBE9] focus:border-blue-500 focus:ring-1 focus:ring-blue-500">
            <?php if($errors->has('license_key')): ?>
                <small class="block mt-2 text-sm font-medium text-[#E93C3C]"><?php echo e($errors->first('license_key')); ?></small>
            <?php endif; ?>
            <?php if($errors->has('global')): ?>
                <small class="block mt-2 text-sm font-medium text-[#E93C3C]"><?php echo e($errors->first('global')); ?></small>
            <?php endif; ?>
        </div>

        <button type="submit"
            class="w-fit mx-auto p-3 px-6 rounded-lg flex items-center justify-center gap-3 bg-primary text-white">
            <span class="text-sm font-medium capitalize"><?php echo e(trans('installer.license.next')); ?></span>
            <i class="fa-solid fa-angle-right text-sm"></i>
        </button>
    </form>

    <div id="installer-modal" class="modal">
        <div class="modal-dialog">
            <div class="modal-header">
                <h3 class="modal-title"><?php echo e(trans('installer.license.active_process')); ?></h3>
                <button class="modal-close fa-solid fa-xmark text-xl text-slate-400 hover:text-red-500"></button>
            </div>
            <div class="modal-body">
                <section class="mb-5">
                    <h4 class="mb-2 font-bold"><?php echo e(__('Step1: ')); ?> <a href="<?php echo e(config('product.officialSite')); ?>"
                            target="_blank"><?php echo e(__(' Go to iNilabs')); ?></a></h4>
                    <picture>
                        <img src="<?php echo e(asset('images/installer/home.png')); ?>" class="img-fluid img-thumbnail image-css"
                            alt="...">
                    </picture>
                </section>
                <section class="mb-5">
                    <h4 class="mb-2 font-bold"><?php echo e(__('Step2: ')); ?> <a href="<?php echo e(config('product.loginUrl')); ?>"
                            target="_blank"><?php echo e(__(' Login to iNilabs')); ?></a></h4>
                    <picture>
                        <img src="<?php echo e(asset('images/installer/login.png')); ?>" class="img-fluid img-thumbnail image-css"
                            alt="...">
                    </picture>
                </section>
                <section class="mb-5">
                    <h4 class="mb-2 font-bold"><?php echo e(__('Step3: ')); ?> <a href="<?php echo e(config('product.activeLicense')); ?>"
                            target="_blank"><?php echo e(__(' Active your license code')); ?> </a></h4>
                    <h6><?php echo e(__('You can easily get the activation code and try to install your product by this code.')); ?>

                    </h6>
                    <picture class="mt-1">
                        <img src="<?php echo e(asset('images/installer/active.png')); ?>" class="img-fluid img-thumbnail image-css"
                            alt="...">
                    </picture>
                </section>
            </div>
        </div>
    </div>

    <script>
        // Adiciona funcionalidade de clique para copiar licenças
        document.addEventListener('DOMContentLoaded', function() {
            const licenseInput = document.querySelector('input[name="license_key"]');
            const licenseCodes = document.querySelectorAll('code');

            licenseCodes.forEach(function(code) {
                code.style.cursor = 'pointer';
                code.title = 'Clique para usar esta licença';

                code.addEventListener('click', function() {
                    const licenseKey = this.textContent.trim();
                    licenseInput.value = licenseKey;

                    // Feedback visual
                    const originalBg = this.style.backgroundColor;
                    this.style.backgroundColor = '#10B981';
                    this.style.color = 'white';

                    setTimeout(() => {
                        this.style.backgroundColor = originalBg;
                        this.style.color = '';
                    }, 500);

                    // Foca no input
                    licenseInput.focus();
                });
            });
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installer.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\resources\views/installer/license.blade.php ENDPATH**/ ?>