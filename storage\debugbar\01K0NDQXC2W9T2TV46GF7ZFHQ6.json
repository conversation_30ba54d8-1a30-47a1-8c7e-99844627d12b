{"__meta": {"id": "01K0NDQXC2W9T2TV46GF7ZFHQ6", "datetime": "2025-07-21 08:41:55", "utime": **********.075839, "method": "GET", "uri": "/api/frontend/product-category/tree", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.654826, "end": **********.075858, "duration": 0.421032190322876, "duration_str": "421ms", "measures": [{"label": "Booting", "start": **********.654826, "relative_start": 0, "end": **********.91073, "relative_end": **********.91073, "duration": 0.***************, "duration_str": "256ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.910742, "relative_start": 0.*****************, "end": **********.07586, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "165ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.935022, "relative_start": 0.****************, "end": **********.941688, "relative_end": **********.941688, "duration": 0.006665945053100586, "duration_str": "6.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.068366, "relative_start": 0.****************, "end": **********.072687, "relative_end": **********.072687, "duration": 0.004320859909057617, "duration_str": "4.32ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.072723, "relative_start": 0.*****************, "end": **********.072751, "relative_end": **********.072751, "duration": 2.8133392333984375e-05, "duration_str": "28μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.06142, "accumulated_duration_str": "61.42ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "with recursive `laravel_cte` as ((select `product_categories`.*, 0 as `depth`, cast(`id` as char(65535)) as `path` from `product_categories` where `parent_id` is null) union all (select `product_categories`.*, `depth` + 1 as `depth`, concat(`path`, '.', `product_categories`.`id`) from `product_categories` inner join `laravel_cte` on `laravel_cte`.`id` = `product_categories`.`parent_id`)) select * from `laravel_cte` where `status` = 5", "type": "query", "params": [], "bindings": [".", 5], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/staudenmeir/laravel-adjacency-list/src/Eloquent/Traits/BuildsAdjacencyListQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-adjacency-list\\src\\Eloquent\\Traits\\BuildsAdjacencyListQueries.php", "line": 26}, {"index": 15, "namespace": null, "name": "app/Services/ProductCategoryService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\ProductCategoryService.php", "line": 63}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/ProductCategoryController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\ProductCategoryController.php", "line": 41}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.9924111, "duration": 0.06142, "duration_str": "61.42ms", "memory": 0, "memory_str": null, "filename": "BuildsAdjacencyListQueries.php:26", "source": {"index": 13, "namespace": null, "name": "vendor/staudenmeir/laravel-adjacency-list/src/Eloquent/Traits/BuildsAdjacencyListQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-adjacency-list\\src\\Eloquent\\Traits\\BuildsAdjacencyListQueries.php", "line": 26}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fstaudenmeir%2Flaravel-adjacency-list%2Fsrc%2FEloquent%2FTraits%2FBuildsAdjacencyListQueries.php&line=26", "ajax": false, "filename": "BuildsAdjacencyListQueries.php", "line": "26"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product-category/tree", "action_name": "frontend.product-category.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@tree", "uri": "GET api/frontend/product-category/tree", "controller": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@tree<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductCategoryController.php&line=38\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/product-category", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FProductCategoryController.php&line=38\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/ProductCategoryController.php:38-45</a>", "middleware": "api, installed, apiKey, localization", "duration": "540ms", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-520873907 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-520873907\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-120727261 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-120727261\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-839024718 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5JRmJPQjlNY3ptcC9QMXNCZWVNTnc9PSIsInZhbHVlIjoiSVdyd1pLZzZUNHdKSWp3V0FWRkx1RVRTZk1LQ29iYjBTK1FhMVlZdnpod2htamdEK0FMWUkyMnlWNDhmSkRlZWZDTXh1WVBDVEFhK0hjdXA2SG5QZUVLWCsxZ0tEZkpTbC82REw2c2w4UnNBR20vNi9XMTQxYkFoMTJoZlZFSG8iLCJtYWMiOiIwMGUzMDE2M2QxYjJjZjllZjkxMTdhN2I4YzkxMjBkYjdmNzYyZThlYzdlOTM4OGZiZTQ3MjdhZmI3NWZhMmQxIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6Ik5JRmJPQjlNY3ptcC9QMXNCZWVNTnc9PSIsInZhbHVlIjoiSVdyd1pLZzZUNHdKSWp3V0FWRkx1RVRTZk1LQ29iYjBTK1FhMVlZdnpod2htamdEK0FMWUkyMnlWNDhmSkRlZWZDTXh1WVBDVEFhK0hjdXA2SG5QZUVLWCsxZ0tEZkpTbC82REw2c2w4UnNBR20vNi9XMTQxYkFoMTJoZlZFSG8iLCJtYWMiOiIwMGUzMDE2M2QxYjJjZjllZjkxMTdhN2I4YzkxMjBkYjdmNzYyZThlYzdlOTM4OGZiZTQ3MjdhZmI3NWZhMmQxIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IkozVFREWUloWE1CNG1scUltUWFHbkE9PSIsInZhbHVlIjoiSzdsU0hGWVprMVA1WWltZTRhbE9PcnJKUGdxbUliUGJTYjdwbTFOa3RISTZXVk5rNHJ3QU1NTWxreFJnZ3dxNVhyeUpKTXpJOWJvdlBLVzZUcEhXYnVaY05iaUZ4T0c1aFNZcWVXUWVkOXVmSERGTWlLaTJldStWQnZvWXhMcUIiLCJtYWMiOiIyYjYxY2EzNGYyYjllMjc1MTE1MzY3OTljZjIzMDljODY3YWY2YjgxM2ZhMTUzNmQyNjk4OTcyNmVhYzc0ZjY3IiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-839024718\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1194436455 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik5JRmJPQjlNY3ptcC9QMXNCZWVNTnc9PSIsInZhbHVlIjoiSVdyd1pLZzZUNHdKSWp3V0FWRkx1RVRTZk1LQ29iYjBTK1FhMVlZdnpod2htamdEK0FMWUkyMnlWNDhmSkRlZWZDTXh1WVBDVEFhK0hjdXA2SG5QZUVLWCsxZ0tEZkpTbC82REw2c2w4UnNBR20vNi9XMTQxYkFoMTJoZlZFSG8iLCJtYWMiOiIwMGUzMDE2M2QxYjJjZjllZjkxMTdhN2I4YzkxMjBkYjdmNzYyZThlYzdlOTM4OGZiZTQ3MjdhZmI3NWZhMmQxIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkozVFREWUloWE1CNG1scUltUWFHbkE9PSIsInZhbHVlIjoiSzdsU0hGWVprMVA1WWltZTRhbE9PcnJKUGdxbUliUGJTYjdwbTFOa3RISTZXVk5rNHJ3QU1NTWxreFJnZ3dxNVhyeUpKTXpJOWJvdlBLVzZUcEhXYnVaY05iaUZ4T0c1aFNZcWVXUWVkOXVmSERGTWlLaTJldStWQnZvWXhMcUIiLCJtYWMiOiIyYjYxY2EzNGYyYjllMjc1MTE1MzY3OTljZjIzMDljODY3YWY2YjgxM2ZhMTUzNmQyNjk4OTcyNmVhYzc0ZjY3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1194436455\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-660857013 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 02:41:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-660857013\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-185230411 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-185230411\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/product-category/tree", "action_name": "frontend.product-category.", "controller_action": "App\\Http\\Controllers\\Frontend\\ProductCategoryController@tree"}, "badge": null}}