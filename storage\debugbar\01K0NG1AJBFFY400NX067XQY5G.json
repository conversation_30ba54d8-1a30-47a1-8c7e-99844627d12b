{"__meta": {"id": "01K0NG1AJBFFY400NX067XQY5G", "datetime": "2025-07-21 09:22:00", "utime": **********.651865, "method": "GET", "uri": "/api/frontend/setting", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.240366, "end": **********.651887, "duration": 0.41152095794677734, "duration_str": "412ms", "measures": [{"label": "Booting", "start": **********.240366, "relative_start": 0, "end": **********.466804, "relative_end": **********.466804, "duration": 0.*****************, "duration_str": "226ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.466816, "relative_start": 0.*****************, "end": **********.651889, "relative_end": 2.1457672119140625e-06, "duration": 0.****************, "duration_str": "185ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.491264, "relative_start": 0.****************, "end": **********.49922, "relative_end": **********.49922, "duration": 0.007955789566040039, "duration_str": "7.96ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.600322, "relative_start": 0.****************, "end": **********.649301, "relative_end": **********.649301, "duration": 0.*****************, "duration_str": "48.98ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.649333, "relative_start": 0.****************, "end": **********.649356, "relative_end": **********.649356, "duration": 2.288818359375e-05, "duration_str": "23μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 15, "nb_statements": 15, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.026399999999999996, "accumulated_duration_str": "26.4ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `payload`, `key` from `settings` where `group` = 'company' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["company"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 12}], "start": **********.515789, "duration": 0.01662, "duration_str": "16.62ms", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 62.955}, {"sql": "select `payload`, `key` from `settings` where `group` = 'site' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["site"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 13}], "start": **********.539463, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 62.955, "width_percent": 3.068}, {"sql": "select `payload`, `key` from `settings` where `group` = 'shipping_setup' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["shipping_setup"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 14}], "start": **********.5474129, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 66.023, "width_percent": 2.992}, {"sql": "select `payload`, `key` from `settings` where `group` = 'theme' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 15}], "start": **********.555369, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 69.015, "width_percent": 2.917}, {"sql": "select `payload`, `key` from `settings` where `group` = 'otp' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["otp"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 16}], "start": **********.5633361, "duration": 0.00049, "duration_str": "490μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 71.932, "width_percent": 1.856}, {"sql": "select `payload`, `key` from `settings` where `group` = 'social_media' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["social_media"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 17}], "start": **********.5705779, "duration": 0.00078, "duration_str": "780μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 73.788, "width_percent": 2.955}, {"sql": "select `payload`, `key` from `settings` where `group` = 'notification' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["notification"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 18}], "start": **********.578164, "duration": 0.0005200000000000001, "duration_str": "520μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 76.742, "width_percent": 1.97}, {"sql": "select `payload`, `key` from `settings` where `group` = 'whatsapp' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["whatsapp"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 19}], "start": **********.5854208, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 78.712, "width_percent": 2.917}, {"sql": "select `payload`, `key` from `settings` where `group` = 'cookies' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["cookies"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 20}], "start": **********.592954, "duration": 0.00061, "duration_str": "610μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 81.629, "width_percent": 2.311}, {"sql": "select * from `settings` where (`key` = 'theme_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.6084762, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 83.939, "width_percent": 2.538}, {"sql": "select * from `media` where `media`.`model_id` in (13) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.617809, "duration": 0.0007099999999999999, "duration_str": "710μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 86.477, "width_percent": 2.689}, {"sql": "select * from `settings` where (`key` = 'theme_footer_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_footer_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.622651, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 89.167, "width_percent": 2.348}, {"sql": "select * from `media` where `media`.`model_id` in (15) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.629566, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 91.515, "width_percent": 3.22}, {"sql": "select * from `settings` where (`key` = 'theme_favicon_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_favicon_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.634352, "duration": 0.0005899999999999999, "duration_str": "590μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 94.735, "width_percent": 2.235}, {"sql": "select * from `media` where `media`.`model_id` in (14) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.640964, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 96.97, "width_percent": 3.03}]}, "models": {"data": {"App\\Models\\ThemeSetting": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FThemeSetting.php&line=1", "ajax": false, "filename": "ThemeSetting.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/setting", "action_name": "frontend.setting.", "controller_action": "App\\Http\\Controllers\\Frontend\\SettingController@index", "uri": "GET api/frontend/setting", "controller": "App\\Http\\Controllers\\Frontend\\SettingController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FSettingController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/setting", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FSettingController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/SettingController.php:20-27</a>", "middleware": "api, installed, apiKey, localization", "duration": "541ms", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-804476174 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-804476174\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-453906680 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-453906680\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2046030415 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilh3NVppZTZJODBsQW9pSUZROUhxWlE9PSIsInZhbHVlIjoieXNPNWhOM09zam9FdTg4OGZxVzZkbmNTWFdYdjV1SWFrMHY4dnI3R0xQYmVhYjJpNnpOUndXeTVzS1ljU0UvZ1FYbGpMOExudHlPMzRyWUMwNFFNTkxWcjB4bGh1dFBjS0QxemxrN2lsNS9WSjlRTFBMZ1JMWGl5a0RyV0VhVUwiLCJtYWMiOiJkN2FjNmE1ZmY2NmRiNTQ5MzNkMjMzOWQ2ZDM3NTI2YTcxOTI4OWM3NWQ4ZDQ3Y2ZmZTFhNjgzZTEwYWY2ZWQ0IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6Ilh3NVppZTZJODBsQW9pSUZROUhxWlE9PSIsInZhbHVlIjoieXNPNWhOM09zam9FdTg4OGZxVzZkbmNTWFdYdjV1SWFrMHY4dnI3R0xQYmVhYjJpNnpOUndXeTVzS1ljU0UvZ1FYbGpMOExudHlPMzRyWUMwNFFNTkxWcjB4bGh1dFBjS0QxemxrN2lsNS9WSjlRTFBMZ1JMWGl5a0RyV0VhVUwiLCJtYWMiOiJkN2FjNmE1ZmY2NmRiNTQ5MzNkMjMzOWQ2ZDM3NTI2YTcxOTI4OWM3NWQ4ZDQ3Y2ZmZTFhNjgzZTEwYWY2ZWQ0IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IjVGTjdpVEtzR0JZMG5ONEVhL1pKUEE9PSIsInZhbHVlIjoiZENlalpZRzFxb2d4ZVlTVVZGOXhka2x5WEh0Qnc4VTZUU1hNN2ZJWUFRT0xDK2tWQ2VjQU5nUlVDU2ZXMmI1ci9ueG5UWWtoRUJ4elNzb3djVTgvb2lDYWVCeFdnUmFHU0h0ckVDRWZFVm5NTmQ2U1I3ZTU2aHNyNThyOUJTSVMiLCJtYWMiOiI3NDEwZDA4YmU2NzE2YjRkODQ3MTE0OTUwOWExZmE3OWIyYzYxMWY0NGMxZDQxMTRiM2ZhZjI2YTVhMmY2YWQwIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2046030415\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-440829137 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilh3NVppZTZJODBsQW9pSUZROUhxWlE9PSIsInZhbHVlIjoieXNPNWhOM09zam9FdTg4OGZxVzZkbmNTWFdYdjV1SWFrMHY4dnI3R0xQYmVhYjJpNnpOUndXeTVzS1ljU0UvZ1FYbGpMOExudHlPMzRyWUMwNFFNTkxWcjB4bGh1dFBjS0QxemxrN2lsNS9WSjlRTFBMZ1JMWGl5a0RyV0VhVUwiLCJtYWMiOiJkN2FjNmE1ZmY2NmRiNTQ5MzNkMjMzOWQ2ZDM3NTI2YTcxOTI4OWM3NWQ4ZDQ3Y2ZmZTFhNjgzZTEwYWY2ZWQ0IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjVGTjdpVEtzR0JZMG5ONEVhL1pKUEE9PSIsInZhbHVlIjoiZENlalpZRzFxb2d4ZVlTVVZGOXhka2x5WEh0Qnc4VTZUU1hNN2ZJWUFRT0xDK2tWQ2VjQU5nUlVDU2ZXMmI1ci9ueG5UWWtoRUJ4elNzb3djVTgvb2lDYWVCeFdnUmFHU0h0ckVDRWZFVm5NTmQ2U1I3ZTU2aHNyNThyOUJTSVMiLCJtYWMiOiI3NDEwZDA4YmU2NzE2YjRkODQ3MTE0OTUwOWExZmE3OWIyYzYxMWY0NGMxZDQxMTRiM2ZhZjI2YTVhMmY2YWQwIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-440829137\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-408964128 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:22:00 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-408964128\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1174252867 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1174252867\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/setting", "action_name": "frontend.setting.", "controller_action": "App\\Http\\Controllers\\Frontend\\SettingController@index"}, "badge": null}}