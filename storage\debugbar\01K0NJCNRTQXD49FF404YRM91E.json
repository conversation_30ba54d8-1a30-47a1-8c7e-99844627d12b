{"__meta": {"id": "01K0NJCNRTQXD49FF404YRM91E", "datetime": "2025-07-21 10:03:09", "utime": **********.723342, "method": "GET", "uri": "/api/admin/country-code", "ip": "::1"}, "messages": {"count": 37, "messages": [{"message": "[10:03:09] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetExists($key) should either be compatible with ArrayAccess::offsetExists(mixed $offset): bool, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 457", "message_html": null, "is_string": false, "label": "warning", "time": **********.405947, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetGet($key) should either be compatible with ArrayAccess::offsetGet(mixed $offset): mixed, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 468", "message_html": null, "is_string": false, "label": "warning", "time": **********.406265, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetSet($key, $value) should either be compatible with ArrayAccess::offsetSet(mixed $offset, mixed $value): void, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 484", "message_html": null, "is_string": false, "label": "warning", "time": **********.40655, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::offsetUnset($key) should either be compatible with ArrayAccess::offsetUnset(mixed $offset): void, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 499", "message_html": null, "is_string": false, "label": "warning", "time": **********.406826, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::count() should either be compatible with Countable::count(): int, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 593", "message_html": null, "is_string": false, "label": "warning", "time": **********.407104, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::getIterator() should either be compatible with IteratorAggregate::getIterator(): Traversable, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 605", "message_html": null, "is_string": false, "label": "warning", "time": **********.407386, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: Return type of PragmaRX\\Coollection\\Package\\Coollection::jsonSerialize() should either be compatible with JsonSerializable::jsonSerialize(): mixed, or the #[\\ReturnTypeWillChange] attribute should be used to temporarily suppress the notice in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\coollection\\src\\package\\Coollection.php on line 579", "message_html": null, "is_string": false, "label": "warning", "time": **********.40766, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.514095, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.518011, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.52261, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.534276, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.568237, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.574313, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.623611, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.626394, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.649096, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.656455, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.675982, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.698791, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.699393, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.700116, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.700979, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.701953, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.70289, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.703838, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.704761, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.70569, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.706617, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.707552, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.708472, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.709396, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.710148, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.710717, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.71129, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.711863, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.712423, "xdebug_link": null, "collector": "log"}, {"message": "[10:03:09] LOG.warning: strtolower(): Passing null to parameter #1 ($string) of type string is deprecated in C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\pragmarx\\countries\\src\\package\\Data\\Repository.php on line 238", "message_html": null, "is_string": false, "label": "warning", "time": **********.713, "xdebug_link": null, "collector": "log"}]}, "time": {"start": **********.008565, "end": **********.723422, "duration": 0.7148571014404297, "duration_str": "715ms", "measures": [{"label": "Booting", "start": **********.008565, "relative_start": 0, "end": **********.283362, "relative_end": **********.283362, "duration": 0.****************, "duration_str": "275ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.283383, "relative_start": 0.*****************, "end": **********.723425, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "440ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.309731, "relative_start": 0.****************, "end": **********.314827, "relative_end": **********.314827, "duration": 0.005095958709716797, "duration_str": "5.1ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.715531, "relative_start": 0.****************, "end": **********.720538, "relative_end": **********.720538, "duration": 0.0050067901611328125, "duration_str": "5.01ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.72058, "relative_start": 0.****************, "end": **********.720608, "relative_end": **********.720608, "duration": 2.7894973754882812e-05, "duration_str": "28μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "74MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.021470000000000003, "accumulated_duration_str": "21.47ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.338755, "duration": 0.018850000000000002, "duration_str": "18.85ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 87.797}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.375153, "duration": 0.00067, "duration_str": "670μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "kesung_loja", "explain": null, "start_percent": 87.797, "width_percent": 3.121}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-21 10:03:09', `personal_access_tokens`.`updated_at` = '2025-07-21 10:03:09' where `id` = 2", "type": "query", "params": [], "bindings": ["2025-07-21 10:03:09", "2025-07-21 10:03:09", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/staudenmeir/laravel-cte/src/Query/Traits/BuildsExpressionQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-cte\\src\\Query\\Traits\\BuildsExpressionQueries.php", "line": 225}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.389711, "duration": 0.00195, "duration_str": "1.95ms", "memory": 0, "memory_str": null, "filename": "BuildsExpressionQueries.php:225", "source": {"index": 10, "namespace": null, "name": "vendor/staudenmeir/laravel-cte/src/Query/Traits/BuildsExpressionQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-cte\\src\\Query\\Traits\\BuildsExpressionQueries.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fstaudenmeir%2Flaravel-cte%2Fsrc%2FQuery%2FTraits%2FBuildsExpressionQueries.php&line=225", "ajax": false, "filename": "BuildsExpressionQueries.php", "line": "225"}, "connection": "kesung_loja", "explain": null, "start_percent": 90.918, "width_percent": 9.082}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/admin/country-code", "action_name": "admin.country-code.", "controller_action": "App\\Http\\Controllers\\Admin\\CountryCodeController@index", "uri": "GET api/admin/country-code", "controller": "App\\Http\\Controllers\\Admin\\CountryCodeController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FCountryCodeController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/admin/country-code", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FCountryCodeController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CountryCodeController.php:19-26</a>", "middleware": "api, auth:sanctum", "duration": "816ms", "peak_memory": "78MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-254960019 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-254960019\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1975502299 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1975502299\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-985103761 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 2|thT******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">pt</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"44 characters\">http://localhost:8000/admin/settings/company</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IjRzUXZSMWFqSmdIWng1ci9BWE9xR3c9PSIsInZhbHVlIjoiUHIwaWNDRlFLR3N5RHZldHdmS2w3ZzcvdjBiZkFRMkZLTGJaWFRZbXhQREJxMHh0VkV0SGYrK1B4NlRxdzc4QytrNXg3SE94N3ZGd1hVYzd6SUp5TWpGWWc4OFpZdHdsNmFWWFdjMjJDZEhFdktTZ2IvMVVrc3pMZ20rRnU3aEYiLCJtYWMiOiJlNTUyNWM5NTRkODgxMjA0MDNhMzFkYTFhNDI2MzZmMjliNjAwMWI0MzM4YjBiMWQxNGFiNjViNDQ3ZmM5ZGVkIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-985103761\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-580551233 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRzUXZSMWFqSmdIWng1ci9BWE9xR3c9PSIsInZhbHVlIjoiUHIwaWNDRlFLR3N5RHZldHdmS2w3ZzcvdjBiZkFRMkZLTGJaWFRZbXhQREJxMHh0VkV0SGYrK1B4NlRxdzc4QytrNXg3SE94N3ZGd1hVYzd6SUp5TWpGWWc4OFpZdHdsNmFWWFdjMjJDZEhFdktTZ2IvMVVrc3pMZ20rRnU3aEYiLCJtYWMiOiJlNTUyNWM5NTRkODgxMjA0MDNhMzFkYTFhNDI2MzZmMjliNjAwMWI0MzM4YjBiMWQxNGFiNjViNDQ3ZmM5ZGVkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-580551233\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1000674821 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 04:03:09 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1000674821\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1102367813 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1102367813\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/admin/country-code", "action_name": "admin.country-code.", "controller_action": "App\\Http\\Controllers\\Admin\\CountryCodeController@index"}, "badge": null}}