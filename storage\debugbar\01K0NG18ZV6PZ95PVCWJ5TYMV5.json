{"__meta": {"id": "01K0NG18ZV6PZ95PVCWJ5TYMV5", "datetime": "2025-07-21 09:21:59", "utime": **********.036592, "method": "GET", "uri": "/home", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.500814, "end": **********.036611, "duration": 0.535797119140625, "duration_str": "536ms", "measures": [{"label": "Booting", "start": **********.500814, "relative_start": 0, "end": **********.881824, "relative_end": **********.881824, "duration": 0.****************, "duration_str": "381ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.881838, "relative_start": 0.****************, "end": **********.036613, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "155ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.920399, "relative_start": 0.****************, "end": **********.930164, "relative_end": **********.930164, "duration": 0.009765148162841797, "duration_str": "9.77ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.004604, "relative_start": 0.****************, "end": **********.033052, "relative_end": **********.033052, "duration": 0.028447866439819336, "duration_str": "28.45ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "View: master", "start": **********.008975, "relative_start": 0.****************, "end": **********.008975, "relative_end": **********.008975, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}, {"label": "View: laravelpwa::meta", "start": **********.030925, "relative_start": 0.****************, "end": **********.030925, "relative_end": **********.030925, "duration": 0, "duration_str": "0μs", "memory": 0, "memory_str": "0B", "params": [], "collector": "views", "group": "View"}]}, "memory": {"peak_usage": 38445200, "peak_usage_str": "37MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 2, "nb_templates": 2, "templates": [{"name": "master", "param_count": null, "params": [], "start": **********.008778, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\resources\\views/master.blade.phpmaster", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fresources%2Fviews%2Fmaster.blade.php&line=1", "ajax": false, "filename": "master.blade.php", "line": "?"}}, {"name": "laravelpwa::meta", "param_count": null, "params": [], "start": **********.030742, "type": "blade", "hash": "bladeC:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\resources\\views/vendor/laravelpwa/meta.blade.phplaravelpwa::meta", "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fresources%2Fviews%2Fvendor%2Flaravelpwa%2Fmeta.blade.php&line=1", "ajax": false, "filename": "meta.blade.php", "line": "?"}}]}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00791, "accumulated_duration_str": "7.91ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `analytics` where (`status` = 5)", "type": "query", "params": [], "bindings": [5], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/RootController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\RootController.php", "line": 15}, {"index": 16, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.956903, "duration": 0.00538, "duration_str": "5.38ms", "memory": 0, "memory_str": null, "filename": "RootController.php:15", "source": {"index": 15, "namespace": null, "name": "app/Http/Controllers/Frontend/RootController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\RootController.php", "line": 15}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FRootController.php&line=15", "ajax": false, "filename": "RootController.php", "line": "15"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 68.015}, {"sql": "select * from `settings` where (`key` = 'theme_favicon_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_favicon_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RootController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\RootController.php", "line": 16}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.979737, "duration": 0.00073, "duration_str": "730μs", "memory": 0, "memory_str": null, "filename": "RootController.php:16", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/RootController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\RootController.php", "line": 16}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FRootController.php&line=16", "ajax": false, "filename": "RootController.php", "line": "16"}, "connection": "kesung_loja", "explain": null, "start_percent": 68.015, "width_percent": 9.229}, {"sql": "select * from `media` where `media`.`model_id` in (14) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.991935, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 77.244, "width_percent": 11.757}, {"sql": "select `payload`, `key` from `settings` where `key` in ('company_name') and `group` = 'company' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["company_name", "company"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 49}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 163}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 66}, {"index": 17, "namespace": "view", "name": "master", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\resources\\views/master.blade.php", "line": 14}], "start": **********.016111, "duration": 0.00087, "duration_str": "870μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 89.001, "width_percent": 10.999}]}, "models": {"data": {"App\\Models\\ThemeSetting": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FThemeSetting.php&line=1", "ajax": false, "filename": "ThemeSetting.php", "line": "?"}}}, "count": 1, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/home", "action_name": null, "controller_action": "App\\Http\\Controllers\\Frontend\\RootController@index", "uri": "GET {any}", "controller": "App\\Http\\Controllers\\Frontend\\RootController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FRootController.php&line=13\" class=\"phpdebugbar-widgets-editor-link\"></a>", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FRootController.php&line=13\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/RootController.php:13-19</a>", "middleware": "web, installed", "duration": "692ms", "peak_memory": "48MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-934689719 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-934689719\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-362529910 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-362529910\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-915328063 data-indent-pad=\"  \"><span class=sf-dump-note>array:13</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"63 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6ImlGQ01ka2pNNWFnc1poSkdmOFBKdFE9PSIsInZhbHVlIjoiOHpQdHRDRkh5YWpRbm8wTXhYNkdqZmRpK2RtUHVhQWFncXNibzdUMzNFQkFFSlh3Smk0emowN0ZnZ1N5UDFOQVVjeG9GaG1HRDFOK0pnR05lcmRYb2ZPd3RsSDBsN0VEWlFyRkJBL3NidzVicVMxU0syL1pxUnJlQTVwdGtXcnIiLCJtYWMiOiIyODhlODFkZTE3ZThkY2FjNDhlODY1OTEyNjU2NDNhNjE5N2JhMGQwMmVjNDhlMDNhYmIzMjBlMzZlZmQzMDI5IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IjV5Vnh4UWorRmlrWXFjWFhqOGZSaGc9PSIsInZhbHVlIjoiSFNtS2dYTmVpU3AyVXE1YmFSK2YvOVErOHBkNFVPWm9keWtBUmFqanJQZUlPSFIzSWM0Y1I0aHJvSDlhaW94UEEzMEl4UG5ZRDlHZEpNWVZaNk82ZFFPRmJMSWJTdzU3MnkvMlBiVitMSUJ5M09VcVlVcG1BZklFSWtnOVlEY1YiLCJtYWMiOiJhYWQ5M2UxOGEwNzg2ZWQ1MjE2ODQ1ODVlMTE2ZGMyNmNlODM5YTY0MmYxNDRhNjRlNWZmODM2NjhhNzUzYTg2IiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"6 characters\">u=0, i</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-915328063\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-143753400 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vOBXC2g4znl2i4WZ0q83wYeEBCJZILQnCV5deX8i</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">dIondrPQDFrxCWHtti0YJHfaYFWTVme3AhY6hqci</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">7uFmoWiPIsunzR9Y87WuSNIjnbhqTJNzEknWSims</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-143753400\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2012953431 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:21:59 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2012953431\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1926967156 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">vOBXC2g4znl2i4WZ0q83wYeEBCJZILQnCV5deX8i</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"21 characters\">http://localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1926967156\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/home", "controller_action": "App\\Http\\Controllers\\Frontend\\RootController@index"}, "badge": null}}