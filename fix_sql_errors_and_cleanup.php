<?php
/**
 * <PERSON>ript para corrigir erros SQL e limpar idiomas desnecessários
 * Mantém apenas o português ativo
 */

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== CORREÇÃO DE ERROS SQL E LIMPEZA DE IDIOMAS ===\n\n";

$errors = [];
$success = [];

// 1. CORRIGIR TABELA ANALYTICS
echo "🔧 ETAPA 1: VERIFICANDO E CORRIGINDO TABELA ANALYTICS\n";
try {
    // Verificar se a tabela analytics existe
    $analyticsExists = DB::select("SHOW TABLES LIKE 'analytics'");
    
    if (empty($analyticsExists)) {
        echo "⚠️ Tabela 'analytics' não existe. Criando...\n";
        
        // Executar migration da analytics
        Artisan::call('migrate', ['--path' => 'database/migrations/2023_07_10_064804_create_analytics_table.php']);
        echo "✅ Tabela 'analytics' criada com sucesso!\n";
        $success[] = "Tabela analytics criada";
    } else {
        echo "✅ Tabela 'analytics' já existe\n";
    }
    
    // Verificar se há dados na tabela analytics
    $analyticsCount = DB::table('analytics')->count();
    echo "📊 Total de registros em analytics: {$analyticsCount}\n";
    
} catch (Exception $e) {
    echo "❌ Erro ao verificar/criar tabela analytics: " . $e->getMessage() . "\n";
    $errors[] = "Analytics: " . $e->getMessage();
}

echo "\n";

// 2. CORRIGIR TABELA SETTINGS
echo "🔧 ETAPA 2: VERIFICANDO TABELA SETTINGS\n";
try {
    // Verificar configurações críticas
    $criticalSettings = [
        'theme_favicon_logo',
        'company_name'
    ];
    
    foreach ($criticalSettings as $setting) {
        $exists = DB::table('settings')->where('key', $setting)->first();
        if (!$exists) {
            echo "⚠️ Configuração '{$setting}' não encontrada. Criando...\n";
            
            $payload = '';
            if ($setting === 'company_name') {
                $payload = 'Shopperzz';
            } elseif ($setting === 'theme_favicon_logo') {
                $payload = '/images/default/favicon.png';
            }
            
            DB::table('settings')->insert([
                'group' => $setting === 'company_name' ? 'company' : 'theme',
                'key' => $setting,
                'payload' => json_encode($payload),
                'created_at' => now(),
                'updated_at' => now()
            ]);
            
            echo "✅ Configuração '{$setting}' criada\n";
            $success[] = "Setting {$setting} criado";
        } else {
            echo "✅ Configuração '{$setting}' existe\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erro ao verificar settings: " . $e->getMessage() . "\n";
    $errors[] = "Settings: " . $e->getMessage();
}

echo "\n";

// 3. LIMPAR IDIOMAS - MANTER APENAS PORTUGUÊS
echo "🗑️ ETAPA 3: REMOVENDO IDIOMAS DESNECESSÁRIOS\n";
try {
    // Listar idiomas atuais
    $languages = DB::table('languages')->get();
    echo "📋 Idiomas encontrados:\n";
    foreach ($languages as $lang) {
        echo "   ID: {$lang->id} | {$lang->name} ({$lang->code})\n";
    }
    
    echo "\n🔄 Removendo idiomas desnecessários...\n";
    
    // Manter apenas o português (pt)
    $languagesToDelete = DB::table('languages')->where('code', '!=', 'pt')->get();
    
    foreach ($languagesToDelete as $lang) {
        echo "🗑️ Removendo: {$lang->name} ({$lang->code})\n";
        
        // Remover mídia associada
        DB::table('media')->where('model_type', 'App\\Models\\Language')
                          ->where('model_id', $lang->id)
                          ->delete();
        
        // Remover o idioma
        DB::table('languages')->where('id', $lang->id)->delete();
        
        echo "✅ {$lang->name} removido com sucesso\n";
        $success[] = "Idioma {$lang->name} removido";
    }
    
    // Garantir que o português está ativo
    $portuguese = DB::table('languages')->where('code', 'pt')->first();
    if ($portuguese) {
        DB::table('languages')->where('code', 'pt')->update([
            'status' => 5, // Status ativo
            'updated_at' => now()
        ]);
        echo "✅ Português definido como idioma ativo\n";
        $success[] = "Português ativado";
    } else {
        echo "❌ Idioma português não encontrado!\n";
        $errors[] = "Português não encontrado";
    }
    
} catch (Exception $e) {
    echo "❌ Erro ao limpar idiomas: " . $e->getMessage() . "\n";
    $errors[] = "Limpeza idiomas: " . $e->getMessage();
}

echo "\n";

// 4. VERIFICAR E CORRIGIR TABELA MEDIA
echo "🔧 ETAPA 4: VERIFICANDO TABELA MEDIA\n";
try {
    $mediaCount = DB::table('media')->count();
    echo "📊 Total de registros em media: {$mediaCount}\n";
    
    // Limpar mídia órfã (sem modelo associado)
    $orphanMedia = DB::table('media')
                     ->whereNotExists(function($query) {
                         $query->select(DB::raw(1))
                               ->from('languages')
                               ->whereRaw('languages.id = media.model_id')
                               ->where('media.model_type', 'App\\Models\\Language');
                     })
                     ->where('model_type', 'App\\Models\\Language')
                     ->count();
    
    if ($orphanMedia > 0) {
        echo "🗑️ Removendo {$orphanMedia} registros de mídia órfã...\n";
        DB::table('media')
          ->whereNotExists(function($query) {
              $query->select(DB::raw(1))
                    ->from('languages')
                    ->whereRaw('languages.id = media.model_id')
                    ->where('media.model_type', 'App\\Models\\Language');
          })
          ->where('model_type', 'App\\Models\\Language')
          ->delete();
        
        echo "✅ Mídia órfã removida\n";
        $success[] = "Mídia órfã removida";
    } else {
        echo "✅ Nenhuma mídia órfã encontrada\n";
    }
    
} catch (Exception $e) {
    echo "❌ Erro ao verificar mídia: " . $e->getMessage() . "\n";
    $errors[] = "Media: " . $e->getMessage();
}

echo "\n";

// 5. LIMPAR CACHE E OTIMIZAR
echo "⚡ ETAPA 5: LIMPANDO CACHE E OTIMIZANDO\n";
try {
    Artisan::call('config:clear');
    echo "✅ Cache de configuração limpo\n";
    
    Artisan::call('cache:clear');
    echo "✅ Cache da aplicação limpo\n";
    
    Artisan::call('view:clear');
    echo "✅ Cache de views limpo\n";
    
    Artisan::call('config:cache');
    echo "✅ Cache de configuração recriado\n";
    
    $success[] = "Cache otimizado";
    
} catch (Exception $e) {
    echo "❌ Erro ao limpar cache: " . $e->getMessage() . "\n";
    $errors[] = "Cache: " . $e->getMessage();
}

echo "\n";

// 6. VERIFICAÇÃO FINAL
echo "🔍 ETAPA 6: VERIFICAÇÃO FINAL\n";
try {
    // Verificar idiomas restantes
    $finalLanguages = DB::table('languages')->get();
    echo "📋 Idiomas finais:\n";
    foreach ($finalLanguages as $lang) {
        $status = $lang->status == 5 ? 'ATIVO' : 'INATIVO';
        echo "   ID: {$lang->id} | {$lang->name} ({$lang->code}) - {$status}\n";
    }
    
    // Verificar tabelas críticas
    $tables = ['analytics', 'settings', 'languages', 'media'];
    echo "\n📊 Status das tabelas:\n";
    foreach ($tables as $table) {
        try {
            $count = DB::table($table)->count();
            echo "   {$table}: {$count} registros ✅\n";
        } catch (Exception $e) {
            echo "   {$table}: ERRO - " . $e->getMessage() . " ❌\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erro na verificação final: " . $e->getMessage() . "\n";
    $errors[] = "Verificação final: " . $e->getMessage();
}

// RELATÓRIO FINAL
echo "\n" . str_repeat("=", 60) . "\n";
echo "📊 RELATÓRIO FINAL\n";
echo str_repeat("=", 60) . "\n";

echo "\n✅ SUCESSOS (" . count($success) . "):\n";
foreach ($success as $item) {
    echo "   • {$item}\n";
}

if (!empty($errors)) {
    echo "\n❌ ERROS (" . count($errors) . "):\n";
    foreach ($errors as $error) {
        echo "   • {$error}\n";
    }
} else {
    echo "\n🎉 NENHUM ERRO ENCONTRADO!\n";
}

echo "\n🎯 RESULTADO:\n";
if (empty($errors)) {
    echo "✅ SISTEMA TOTALMENTE CORRIGIDO E OTIMIZADO!\n";
    echo "✅ Apenas o idioma PORTUGUÊS está ativo\n";
    echo "✅ Erros SQL corrigidos\n";
    echo "✅ Tabelas verificadas e funcionais\n";
    echo "✅ Cache otimizado\n";
} else {
    echo "⚠️ Sistema parcialmente corrigido\n";
    echo "   Verifique os erros acima e execute novamente se necessário\n";
}

echo "\n🌐 PRÓXIMOS PASSOS:\n";
echo "1. Acesse: http://localhost:8000\n";
echo "2. Login: <EMAIL> / 123456\n";
echo "3. Sistema agora apenas em PORTUGUÊS! 🇧🇷\n";

echo "\n" . str_repeat("=", 60) . "\n";
echo "🎉 CORREÇÃO COMPLETA FINALIZADA!\n";
echo "📅 " . date('d/m/Y H:i:s') . "\n";
echo str_repeat("=", 60) . "\n";
?>
