<!DOCTYPE svg PUBLIC "-//W3C//DTD SVG 1.1//EN" "http://www.w3.org/Graphics/SVG/1.1/DTD/svg11.dtd">
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" xml:space="preserve" x="0"
 y="0" viewBox="0 0 <?php echo e($originalImageWidth); ?> <?php echo e($originalImageHeight); ?>">
	<image width="<?php echo e($originalImageWidth); ?>" height="<?php echo e($originalImageHeight); ?>" xlink:href="<?php echo e($tinyImageBase64); ?>">
	</image>
</svg><?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\vendor\spatie\laravel-medialibrary\resources\views\placeholderSvg.blade.php ENDPATH**/ ?>