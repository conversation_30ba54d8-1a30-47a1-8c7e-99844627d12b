@echo off
echo ========================================
echo   REMOVENDO INSTALACAO ANTIGA
echo ========================================
echo.

set OLD_DIR=c:\Users\<USER>\Desktop\xamp8.1\htdocs
set NEW_DIR=c:\Users\<USER>\Desktop\xamp8.1\shopperzz-novo

echo 🗑️ Removendo instalacao antiga...
echo Diretorio: %OLD_DIR%
echo.

REM Parar processos PHP
echo ⏹️ Parando processos PHP...
taskkill /F /IM php.exe /T >nul 2>&1
timeout /t 3 >nul

REM Remover diretorio antigo
if exist "%OLD_DIR%" (
    echo 🗑️ Removendo diretorio antigo...
    rmdir /s /q "%OLD_DIR%" >nul 2>&1
    
    if not exist "%OLD_DIR%" (
        echo ✅ Instalacao antiga removida com sucesso!
    ) else (
        echo ⚠️ Alguns arquivos podem estar em uso
        echo Tentando metodo alternativo...
        rd /s /q "%OLD_DIR%" >nul 2>&1
        
        if not exist "%OLD_DIR%" (
            echo ✅ Instalacao antiga removida!
        ) else (
            echo ⚠️ Diretorio ainda existe - alguns arquivos em uso
        )
    )
) else (
    echo ✅ Instalacao antiga ja foi removida
)

echo.
echo ========================================
echo   RESULTADO FINAL
echo ========================================
echo.

if exist "%NEW_DIR%" (
    echo ✅ Nova instalacao disponivel em:
    echo    %NEW_DIR%
    echo.
    echo 🌐 Para iniciar o servidor:
    echo    cd "%NEW_DIR%"
    echo    C:\xampp\php\php.exe -S localhost:8001 -t public
    echo.
    echo 🌐 Acesso:
    echo    Frontend: http://localhost:8001
    echo    Admin: http://localhost:8001/admin
    echo    Login: <EMAIL> / 123456
    echo.
    echo 📱 PWA: Instalavel pelo navegador
    echo 🇧🇷 Idioma: Apenas Portugues
) else (
    echo ❌ Nova instalacao nao encontrada!
)

if not exist "%OLD_DIR%" (
    echo ✅ Instalacao antiga removida completamente
) else (
    echo ⚠️ Instalacao antiga ainda existe
)

echo.
echo ========================================
echo   LIMPEZA CONCLUIDA!
echo ========================================
echo.
echo Pressione qualquer tecla para continuar...
pause >nul
