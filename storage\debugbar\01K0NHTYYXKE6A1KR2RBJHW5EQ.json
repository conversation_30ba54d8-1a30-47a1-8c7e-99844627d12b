{"__meta": {"id": "01K0NHTYYXKE6A1KR2RBJHW5EQ", "datetime": "2025-07-21 09:53:29", "utime": **********.310359, "method": "GET", "uri": "/api/frontend/setting", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1753070008.860831, "end": **********.310384, "duration": 0.4495530128479004, "duration_str": "450ms", "measures": [{"label": "Booting", "start": 1753070008.860831, "relative_start": 0, "end": **********.11768, "relative_end": **********.11768, "duration": 0.****************, "duration_str": "257ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.117693, "relative_start": 0.*****************, "end": **********.310386, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "193ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.141643, "relative_start": 0.*****************, "end": **********.147957, "relative_end": **********.147957, "duration": 0.*****************, "duration_str": "6.31ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.253957, "relative_start": 0.****************, "end": **********.307719, "relative_end": **********.307719, "duration": 0.053761959075927734, "duration_str": "53.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.307755, "relative_start": 0.*****************, "end": **********.307778, "relative_end": **********.307778, "duration": 2.288818359375e-05, "duration_str": "23μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 15, "nb_statements": 15, "nb_visible_statements": 15, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02778, "accumulated_duration_str": "27.78ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select `payload`, `key` from `settings` where `group` = 'company' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["company"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 12}], "start": **********.1644008, "duration": 0.01763, "duration_str": "17.63ms", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 63.463}, {"sql": "select `payload`, `key` from `settings` where `group` = 'site' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["site"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 13}], "start": **********.1900542, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 63.463, "width_percent": 2.304}, {"sql": "select `payload`, `key` from `settings` where `group` = 'shipping_setup' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["shipping_setup"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 14}], "start": **********.198869, "duration": 0.0005600000000000001, "duration_str": "560μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 65.767, "width_percent": 2.016}, {"sql": "select `payload`, `key` from `settings` where `group` = 'theme' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["theme"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 15}], "start": **********.206277, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 67.783, "width_percent": 2.7}, {"sql": "select `payload`, `key` from `settings` where `group` = 'otp' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["otp"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 16}], "start": **********.214728, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 70.482, "width_percent": 2.88}, {"sql": "select `payload`, `key` from `settings` where `group` = 'social_media' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["social_media"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 17}], "start": **********.2230551, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 73.362, "width_percent": 2.88}, {"sql": "select `payload`, `key` from `settings` where `group` = 'notification' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["notification"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 18}], "start": **********.230947, "duration": 0.0008, "duration_str": "800μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 76.242, "width_percent": 2.88}, {"sql": "select `payload`, `key` from `settings` where `group` = 'whatsapp' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["whatsapp"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 19}], "start": **********.2386868, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 79.122, "width_percent": 2.844}, {"sql": "select `payload`, `key` from `settings` where `group` = 'cookies' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["cookies"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/SettingService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\SettingService.php", "line": 20}], "start": **********.246333, "duration": 0.00079, "duration_str": "790μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 81.965, "width_percent": 2.844}, {"sql": "select * from `settings` where (`key` = 'theme_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 44}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.262139, "duration": 0.00062, "duration_str": "620μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 84.809, "width_percent": 2.232}, {"sql": "select * from `media` where `media`.`model_id` in (13) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.2713811, "duration": 0.00085, "duration_str": "850μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 87.041, "width_percent": 3.06}, {"sql": "select * from `settings` where (`key` = 'theme_footer_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_footer_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 45}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.2804081, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 90.101, "width_percent": 2.304}, {"sql": "select * from `media` where `media`.`model_id` in (15) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.287329, "duration": 0.00075, "duration_str": "750μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 92.405, "width_percent": 2.7}, {"sql": "select * from `settings` where (`key` = 'theme_favicon_logo') limit 1", "type": "query", "params": [], "bindings": ["theme_favicon_logo"], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, {"index": 17, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 46}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 109}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/ResourceResponse.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\ResourceResponse.php", "line": 39}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Http/Resources/Json/JsonResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Resources\\Json\\JsonResource.php", "line": 245}], "start": **********.2921362, "duration": 0.00064, "duration_str": "640μs", "memory": 0, "memory_str": null, "filename": "SettingResource.php:81", "source": {"index": 16, "namespace": null, "name": "app/Http/Resources/SettingResource.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Resources\\SettingResource.php", "line": 81}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FResources%2FSettingResource.php&line=81", "ajax": false, "filename": "SettingResource.php", "line": "81"}, "connection": "kesung_loja", "explain": null, "start_percent": 95.104, "width_percent": 2.304}, {"sql": "select * from `media` where `media`.`model_id` in (14) and `media`.`model_type` = 'App\\\\Models\\\\ThemeSetting'", "type": "query", "params": [], "bindings": ["App\\Models\\ThemeSetting"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.298853, "duration": 0.0007199999999999999, "duration_str": "720μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 97.408, "width_percent": 2.592}]}, "models": {"data": {"App\\Models\\ThemeSetting": {"value": 3, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FThemeSetting.php&line=1", "ajax": false, "filename": "ThemeSetting.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/setting", "action_name": "frontend.setting.", "controller_action": "App\\Http\\Controllers\\Frontend\\SettingController@index", "uri": "GET api/frontend/setting", "controller": "App\\Http\\Controllers\\Frontend\\SettingController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FSettingController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/setting", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FSettingController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/SettingController.php:20-27</a>", "middleware": "api, installed, apiKey, localization", "duration": "578ms", "peak_memory": "48MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1147326144 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1147326144\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-329257122 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-329257122\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1003356714 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-gpc</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"781 characters\">XSRF-TOKEN=eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6InoybndEdnhHdnpHdVFCbjdaSzZDTlE9PSIsInZhbHVlIjoiZ3g0SXFkNU02dW9HYzh6VUYrbVgzVEVpeEpkTWMzbEZuVjdKQW1QVHFLRzJ5OWVYUUMxekk4aUtoVDFKaDBoR094eFF3VmJXMFY1VVZMUEVnWklrTXlKWkkwaWhCcmhueCtXODRkOXRxS05PaTRKMzZqQml2RTkrZ0tLTy9GdXgiLCJtYWMiOiJlYjdiNjlkOThmZTdjYWEwNzk5N2Y3NTcxMTgwNzJjNjQ1ZmJmMDQzMmJkMjcxZDIwYTJiYWE3NDViNWE4ZjBhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1003356714\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-739927346 data-indent-pad=\"  \"><span class=sf-dump-note>array:2</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9yNkxvNjZlZW9XMmRBajBianh4WkE9PSIsInZhbHVlIjoic1phRzQ1dGpBV0xtRFV0OFBqbzRKSmR4Rmd3aFJDbGpPbEs2NEZMUWRnTGlYd1AwNlVPUml3OWdPSE9MUE52aXdkUEhrVnpYRlJtc2dSNjRRUll2Q056NExqVDViR0NiRVZXY2p6c0hCU3E2UUNqL1U4VmszUmtNb0x6clNZdTkiLCJtYWMiOiJjMDNlMzkwZGQ3NjgwMTJiMDJlYzJhNGZjNmUyOWU1YWEzYTcwODQ3ZjFlZjMzYzg2MWFhMjhhZmRhY2NlMTIyIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6InoybndEdnhHdnpHdVFCbjdaSzZDTlE9PSIsInZhbHVlIjoiZ3g0SXFkNU02dW9HYzh6VUYrbVgzVEVpeEpkTWMzbEZuVjdKQW1QVHFLRzJ5OWVYUUMxekk4aUtoVDFKaDBoR094eFF3VmJXMFY1VVZMUEVnWklrTXlKWkkwaWhCcmhueCtXODRkOXRxS05PaTRKMzZqQml2RTkrZ0tLTy9GdXgiLCJtYWMiOiJlYjdiNjlkOThmZTdjYWEwNzk5N2Y3NTcxMTgwNzJjNjQ1ZmJmMDQzMmJkMjcxZDIwYTJiYWE3NDViNWE4ZjBhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739927346\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1230256285 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:53:29 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1230256285\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-2057302360 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-2057302360\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/setting", "action_name": "frontend.setting.", "controller_action": "App\\Http\\Controllers\\Frontend\\SettingController@index"}, "badge": null}}