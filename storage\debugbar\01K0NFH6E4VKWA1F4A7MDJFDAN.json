{"__meta": {"id": "01K0NFH6E4VKWA1F4A7MDJFDAN", "datetime": "2025-07-21 09:13:12", "utime": **********.133179, "method": "GET", "uri": "/api/admin/setting/unit?paginate=0&order_column=id&order_type=asc", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.764392, "end": **********.133194, "duration": 0.3688020706176758, "duration_str": "369ms", "measures": [{"label": "Booting", "start": **********.764392, "relative_start": 0, "end": **********.981203, "relative_end": **********.981203, "duration": 0.****************, "duration_str": "217ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.981215, "relative_start": 0.*****************, "end": **********.133197, "relative_end": 3.0994415283203125e-06, "duration": 0.*****************, "duration_str": "152ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.004954, "relative_start": 0.*****************, "end": **********.00973, "relative_end": **********.00973, "duration": 0.****************, "duration_str": "4.78ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.126421, "relative_start": 0.****************, "end": **********.130774, "relative_end": **********.130774, "duration": 0.004353046417236328, "duration_str": "4.35ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.130798, "relative_start": 0.****************, "end": **********.13082, "relative_end": **********.13082, "duration": 2.193450927734375e-05, "duration_str": "22μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02605, "accumulated_duration_str": "26.05ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.033008, "duration": 0.02298, "duration_str": "22.98ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 88.215}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.0865662, "duration": 0.00044, "duration_str": "440μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "kesung_loja", "explain": null, "start_percent": 88.215, "width_percent": 1.689}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-21 09:13:12', `personal_access_tokens`.`updated_at` = '2025-07-21 09:13:12' where `id` = 1", "type": "query", "params": [], "bindings": ["2025-07-21 09:13:12", "2025-07-21 09:13:12", 1], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/staudenmeir/laravel-cte/src/Query/Traits/BuildsExpressionQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-cte\\src\\Query\\Traits\\BuildsExpressionQueries.php", "line": 225}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.094066, "duration": 0.0021000000000000003, "duration_str": "2.1ms", "memory": 0, "memory_str": null, "filename": "BuildsExpressionQueries.php:225", "source": {"index": 10, "namespace": null, "name": "vendor/staudenmeir/laravel-cte/src/Query/Traits/BuildsExpressionQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-cte\\src\\Query\\Traits\\BuildsExpressionQueries.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fstaudenmeir%2Flaravel-cte%2Fsrc%2FQuery%2FTraits%2FBuildsExpressionQueries.php&line=225", "ajax": false, "filename": "BuildsExpressionQueries.php", "line": "225"}, "connection": "kesung_loja", "explain": null, "start_percent": 89.904, "width_percent": 8.061}, {"sql": "select * from `units` order by `id` asc", "type": "query", "params": [], "bindings": [], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/UnitService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\UnitService.php", "line": 40}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Admin/UnitController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Admin\\UnitController.php", "line": 27}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.118442, "duration": 0.00053, "duration_str": "530μs", "memory": 0, "memory_str": null, "filename": "UnitService.php:40", "source": {"index": 15, "namespace": null, "name": "app/Services/UnitService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\UnitService.php", "line": 40}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FServices%2FUnitService.php&line=40", "ajax": false, "filename": "UnitService.php", "line": "40"}, "connection": "kesung_loja", "explain": null, "start_percent": 97.965, "width_percent": 2.035}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/admin/setting/unit?order_column=id&order_type=asc&paginate=0", "action_name": "admin.setting.unit.", "controller_action": "App\\Http\\Controllers\\Admin\\UnitController@index", "uri": "GET api/admin/setting/unit", "controller": "App\\Http\\Controllers\\Admin\\UnitController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FUnitController.php&line=24\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/admin/setting/unit", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FUnitController.php&line=24\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/UnitController.php:24-31</a>", "middleware": "api, auth:sanctum", "duration": "480ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-613894749 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>paginate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>order_column</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order_type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-613894749\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-545613136 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-545613136\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1029299230 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjFoeDM0WjRmTUNtMjl2N0s3QTZtNnc9PSIsInZhbHVlIjoiTDNKOEp4cmRrYVRkVHA1WGM5WlpJYWU2Um5ZdFpvbEJuZDdRZGVzWnRURGs3ZklrNGRHQVVzUUR6bm5IcnU5NU52c3liczVFVmZyTmM1bGZYai82bnp4am45MlBsSDdJaUt4RitQMUtlcTFaRCt4NEwwOUk1ckd5bmtIMTZQQWUiLCJtYWMiOiJjMjk2MDBmZTYyYzU0NGZhZjJiNTU4NDVlMTc5OGYxODc0MjY5Y2MyZTcwMTM0OGY1ZTU5MDA2NjQ0MjdkY2U1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"36 characters\">http://localhost:8000/admin/products</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6IjFoeDM0WjRmTUNtMjl2N0s3QTZtNnc9PSIsInZhbHVlIjoiTDNKOEp4cmRrYVRkVHA1WGM5WlpJYWU2Um5ZdFpvbEJuZDdRZGVzWnRURGs3ZklrNGRHQVVzUUR6bm5IcnU5NU52c3liczVFVmZyTmM1bGZYai82bnp4am45MlBsSDdJaUt4RitQMUtlcTFaRCt4NEwwOUk1ckd5bmtIMTZQQWUiLCJtYWMiOiJjMjk2MDBmZTYyYzU0NGZhZjJiNTU4NDVlMTc5OGYxODc0MjY5Y2MyZTcwMTM0OGY1ZTU5MDA2NjQ0MjdkY2U1IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IkRUQmovNitnci9INU13eGg0ZkZkc0E9PSIsInZhbHVlIjoiZzVZTkJacXd2RlFZN1JjcTBOUEZ1Ujl3UE5VM0NBOVZac0xWYXpDb0FRTlR4OEVvTVNXRnpsTmo3YmF6dUdJa0wvR0JVejg5eEExRlpWdTNTSVpGMnNPZDJoNkNmZ3Vva2VyUWQyMEoybTROeHVveHRKNW9oQW4yRDRIcXcvNGMiLCJtYWMiOiI1ZDEzZWEyNmEzN2JmMDljOTVjM2FhZDZhMTBkMzRmOGQ0ZmIyOTlhYWQzOTFjYmY1YzJmYWI2MDQyMzBlNGU5IiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1029299230\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-152911075 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjFoeDM0WjRmTUNtMjl2N0s3QTZtNnc9PSIsInZhbHVlIjoiTDNKOEp4cmRrYVRkVHA1WGM5WlpJYWU2Um5ZdFpvbEJuZDdRZGVzWnRURGs3ZklrNGRHQVVzUUR6bm5IcnU5NU52c3liczVFVmZyTmM1bGZYai82bnp4am45MlBsSDdJaUt4RitQMUtlcTFaRCt4NEwwOUk1ckd5bmtIMTZQQWUiLCJtYWMiOiJjMjk2MDBmZTYyYzU0NGZhZjJiNTU4NDVlMTc5OGYxODc0MjY5Y2MyZTcwMTM0OGY1ZTU5MDA2NjQ0MjdkY2U1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkRUQmovNitnci9INU13eGg0ZkZkc0E9PSIsInZhbHVlIjoiZzVZTkJacXd2RlFZN1JjcTBOUEZ1Ujl3UE5VM0NBOVZac0xWYXpDb0FRTlR4OEVvTVNXRnpsTmo3YmF6dUdJa0wvR0JVejg5eEExRlpWdTNTSVpGMnNPZDJoNkNmZ3Vva2VyUWQyMEoybTROeHVveHRKNW9oQW4yRDRIcXcvNGMiLCJtYWMiOiI1ZDEzZWEyNmEzN2JmMDljOTVjM2FhZDZhMTBkMzRmOGQ0ZmIyOTlhYWQzOTFjYmY1YzJmYWI2MDQyMzBlNGU5IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-152911075\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-2104244268 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:13:12 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2104244268\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1878647878 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1878647878\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/admin/setting/unit?order_column=id&order_type=asc&paginate=0", "action_name": "admin.setting.unit.", "controller_action": "App\\Http\\Controllers\\Admin\\UnitController@index"}, "badge": null}}