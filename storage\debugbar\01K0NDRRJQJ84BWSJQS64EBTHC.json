{"__meta": {"id": "01K0NDRRJQJ84BWSJQS64EBTHC", "datetime": "2025-07-21 08:42:22", "utime": **********.935925, "method": "GET", "uri": "/api/admin/dashboard/total-orders", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.532112, "end": **********.935942, "duration": 0.40383005142211914, "duration_str": "404ms", "measures": [{"label": "Booting", "start": **********.532112, "relative_start": 0, "end": **********.796305, "relative_end": **********.796305, "duration": 0.***************, "duration_str": "264ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.796317, "relative_start": 0.****************, "end": **********.935944, "relative_end": 2.1457672119140625e-06, "duration": 0.*****************, "duration_str": "140ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.82133, "relative_start": 0.****************, "end": **********.825195, "relative_end": **********.825195, "duration": 0.0038650035858154297, "duration_str": "3.87ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.929698, "relative_start": 0.****************, "end": **********.933372, "relative_end": **********.933372, "duration": 0.003674030303955078, "duration_str": "3.67ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.933404, "relative_start": 0.***************, "end": **********.933431, "relative_end": **********.933431, "duration": 2.6941299438476562e-05, "duration_str": "27μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "39MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 5, "nb_statements": 5, "nb_visible_statements": 5, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02193, "accumulated_duration_str": "21.93ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.85114, "duration": 0.01896, "duration_str": "18.96ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 86.457}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.8861399, "duration": 0.00076, "duration_str": "760μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "kesung_loja", "explain": null, "start_percent": 86.457, "width_percent": 3.466}, {"sql": "select `permissions`.*, `model_has_permissions`.`model_id` as `pivot_model_id`, `model_has_permissions`.`permission_id` as `pivot_permission_id`, `model_has_permissions`.`model_type` as `pivot_model_type` from `permissions` inner join `model_has_permissions` on `permissions`.`id` = `model_has_permissions`.`permission_id` where `model_has_permissions`.`model_id` = 1 and `model_has_permissions`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": [1, "App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, {"index": 21, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/Access/Gate.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Access\\Gate.php", "line": 566}], "start": **********.905114, "duration": 0.00093, "duration_str": "930μs", "memory": 0, "memory_str": null, "filename": "HasPermissions.php:305", "source": {"index": 20, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 305}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasPermissions.php&line=305", "ajax": false, "filename": "HasPermissions.php", "line": "305"}, "connection": "kesung_loja", "explain": null, "start_percent": 89.922, "width_percent": 4.241}, {"sql": "select `roles`.*, `model_has_roles`.`model_id` as `pivot_model_id`, `model_has_roles`.`role_id` as `pivot_role_id`, `model_has_roles`.`model_type` as `pivot_model_type` from `roles` inner join `model_has_roles` on `roles`.`id` = `model_has_roles`.`role_id` where `model_has_roles`.`model_id` in (1) and `model_has_roles`.`model_type` = 'App\\\\Models\\\\User'", "type": "query", "params": [], "bindings": ["App\\Models\\User"], "hints": null, "show_copy": true, "backtrace": [{"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 291}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 193}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasPermissions.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasPermissions.php", "line": 244}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-permission/src/PermissionRegistrar.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\PermissionRegistrar.php", "line": 136}], "start": **********.9120219, "duration": 0.0007700000000000001, "duration_str": "770μs", "memory": 0, "memory_str": null, "filename": "HasRoles.php:188", "source": {"index": 22, "namespace": null, "name": "vendor/spatie/laravel-permission/src/Traits/HasRoles.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-permission\\src\\Traits\\HasRoles.php", "line": 188}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FTraits%2FHasRoles.php&line=188", "ajax": false, "filename": "HasRoles.php", "line": "188"}, "connection": "kesung_loja", "explain": null, "start_percent": 94.163, "width_percent": 3.511}, {"sql": "select count(*) as aggregate from `orders` where `status` = 10", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": true, "backtrace": [{"index": 16, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\DashboardService.php", "line": 190}, {"index": 17, "namespace": null, "name": "app/Http/Controllers/Admin/DashboardController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Admin\\DashboardController.php", "line": 53}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.923079, "duration": 0.00051, "duration_str": "510μs", "memory": 0, "memory_str": null, "filename": "DashboardService.php:190", "source": {"index": 16, "namespace": null, "name": "app/Services/DashboardService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\DashboardService.php", "line": 190}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FServices%2FDashboardService.php&line=190", "ajax": false, "filename": "DashboardService.php", "line": "190"}, "connection": "kesung_loja", "explain": null, "start_percent": 97.674, "width_percent": 2.326}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}, "Spatie\\Permission\\Models\\Role": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-permission%2Fsrc%2FModels%2FRole.php&line=1", "ajax": false, "filename": "Role.php", "line": "?"}}}, "count": 3, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 1, "messages": [{"message": "[\n  ability => dashboard,\n  target => null,\n  result => true,\n  user => 1,\n  arguments => []\n]", "message_html": "<pre class=sf-dump id=sf-dump-307601414 data-indent-pad=\"  \"><span class=sf-dump-note>dashboard </span> [<samp data-depth=1 class=sf-dump-compact>\n  \"<span class=sf-dump-key>ability</span>\" => \"<span class=sf-dump-str title=\"9 characters\">dashboard</span>\"\n  \"<span class=sf-dump-key>target</span>\" => <span class=sf-dump-const>null</span>\n  \"<span class=sf-dump-key>result</span>\" => <span class=sf-dump-const>true</span>\n  \"<span class=sf-dump-key>user</span>\" => <span class=sf-dump-num>1</span>\n  \"<span class=sf-dump-key>arguments</span>\" => \"<span class=sf-dump-str title=\"2 characters\">[]</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-307601414\", {\"maxDepth\":0})</script>\n", "is_string": false, "label": "success", "time": **********.921536, "xdebug_link": null}]}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/admin/dashboard/total-orders", "action_name": "admin.dashboard.", "controller_action": "App\\Http\\Controllers\\Admin\\DashboardController@totalOrders", "uri": "GET api/admin/dashboard/total-orders", "controller": "App\\Http\\Controllers\\Admin\\DashboardController@totalOrders<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=50\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/admin/dashboard", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FDashboardController.php&line=50\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/DashboardController.php:50-57</a>", "middleware": "api, auth:sanctum", "duration": "554ms", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-520260940 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-520260940\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1312599807 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1312599807\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1086830260 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IndtamxQVzBKUWFWZnV5U1pNV1BBOGc9PSIsInZhbHVlIjoiUGpDaHhRWE1GRVFvV1lHVzdMUFN4Smd2ZkRvenZFZ2pMV1RYd0xnVEVLWkRnSWpqU3k2RWxmZWkxM2kxS0RFSUd6a1M2bEhrbGxUaEZNQVN0dndkQ1Z2eDgzM3B5eHVuZS8yYUxhUlBPTWZ4NFZ6WmhWTkpQakh3Sk5wODU4VHIiLCJtYWMiOiI3ODFhMDRjYjVhZWRkNjE3YjYyMzQ0MTJhYTQ5MTFkNGZjOWI5ODBhODc3ODZjYWQyMjgyMWI4YjI5ZGUyMWQ1IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6IndtamxQVzBKUWFWZnV5U1pNV1BBOGc9PSIsInZhbHVlIjoiUGpDaHhRWE1GRVFvV1lHVzdMUFN4Smd2ZkRvenZFZ2pMV1RYd0xnVEVLWkRnSWpqU3k2RWxmZWkxM2kxS0RFSUd6a1M2bEhrbGxUaEZNQVN0dndkQ1Z2eDgzM3B5eHVuZS8yYUxhUlBPTWZ4NFZ6WmhWTkpQakh3Sk5wODU4VHIiLCJtYWMiOiI3ODFhMDRjYjVhZWRkNjE3YjYyMzQ0MTJhYTQ5MTFkNGZjOWI5ODBhODc3ODZjYWQyMjgyMWI4YjI5ZGUyMWQ1IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IkcrdSs1VStQd3RjMWdjLzBQaUs3bXc9PSIsInZhbHVlIjoiMTkzYm8rb29NVThVNkRNVFFpZUhSeFlINkJZcVRSK1ZRaWUyOFZtOE9rZVdYNDU4UktTOWVyWG5YS3B3NEFnWUJybFRUbWh6cFlOaWhDMkx0NzQwbFMwWGpTTzVRejhVSllwdDRLN0tOaHJUUkJuemgzYXMzYWE1YUVaemNVbmoiLCJtYWMiOiI5ZTc4ZjUwMGQwNzI4NDlkM2UzOTA2MzgxNjI3NTYwNDlhMzI1MzI5Nzk5M2I1NTAzZDc4NmNkYjdmODA3MDRhIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1086830260\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1761757941 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IndtamxQVzBKUWFWZnV5U1pNV1BBOGc9PSIsInZhbHVlIjoiUGpDaHhRWE1GRVFvV1lHVzdMUFN4Smd2ZkRvenZFZ2pMV1RYd0xnVEVLWkRnSWpqU3k2RWxmZWkxM2kxS0RFSUd6a1M2bEhrbGxUaEZNQVN0dndkQ1Z2eDgzM3B5eHVuZS8yYUxhUlBPTWZ4NFZ6WmhWTkpQakh3Sk5wODU4VHIiLCJtYWMiOiI3ODFhMDRjYjVhZWRkNjE3YjYyMzQ0MTJhYTQ5MTFkNGZjOWI5ODBhODc3ODZjYWQyMjgyMWI4YjI5ZGUyMWQ1IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkcrdSs1VStQd3RjMWdjLzBQaUs3bXc9PSIsInZhbHVlIjoiMTkzYm8rb29NVThVNkRNVFFpZUhSeFlINkJZcVRSK1ZRaWUyOFZtOE9rZVdYNDU4UktTOWVyWG5YS3B3NEFnWUJybFRUbWh6cFlOaWhDMkx0NzQwbFMwWGpTTzVRejhVSllwdDRLN0tOaHJUUkJuemgzYXMzYWE1YUVaemNVbmoiLCJtYWMiOiI5ZTc4ZjUwMGQwNzI4NDlkM2UzOTA2MzgxNjI3NTYwNDlhMzI1MzI5Nzk5M2I1NTAzZDc4NmNkYjdmODA3MDRhIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1761757941\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-606911348 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 02:42:22 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-606911348\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1284050814 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1284050814\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/admin/dashboard/total-orders", "action_name": "admin.dashboard.", "controller_action": "App\\Http\\Controllers\\Admin\\DashboardController@totalOrders"}, "badge": null}}