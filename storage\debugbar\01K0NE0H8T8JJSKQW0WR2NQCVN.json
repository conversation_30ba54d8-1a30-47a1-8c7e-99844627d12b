{"__meta": {"id": "01K0NE0H8T8JJSKQW0WR2NQCVN", "datetime": "2025-07-21 08:46:37", "utime": **********.595356, "method": "GET", "uri": "/api/frontend/language/show/1", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.250175, "end": **********.595374, "duration": 0.3451991081237793, "duration_str": "345ms", "measures": [{"label": "Booting", "start": **********.250175, "relative_start": 0, "end": **********.473206, "relative_end": **********.473206, "duration": 0.*****************, "duration_str": "223ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.473218, "relative_start": 0.*****************, "end": **********.595377, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "122ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.497051, "relative_start": 0.*****************, "end": **********.503295, "relative_end": **********.503295, "duration": 0.*****************, "duration_str": "6.24ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.553741, "relative_start": 0.*****************, "end": **********.592175, "relative_end": **********.592175, "duration": 0.*****************, "duration_str": "38.43ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.592214, "relative_start": 0.****************, "end": **********.592249, "relative_end": **********.592249, "duration": 3.4809112548828125e-05, "duration_str": "35μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 2, "nb_statements": 2, "nb_visible_statements": 2, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01599, "accumulated_duration_str": "15.99ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `languages` where `id` = '1' limit 1", "type": "query", "params": [], "bindings": ["1"], "hints": null, "show_copy": true, "backtrace": [{"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Router.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php", "line": 959}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php", "line": 41}, {"index": 22, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 183}, {"index": 23, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php", "line": 119}], "start": **********.5169692, "duration": 0.01502, "duration_str": "15.02ms", "memory": 0, "memory_str": null, "filename": "ImplicitRouteBinding.php:61", "source": {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ImplicitRouteBinding.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ImplicitRouteBinding.php", "line": 61}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FImplicitRouteBinding.php&line=61", "ajax": false, "filename": "ImplicitRouteBinding.php", "line": "61"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 93.934}, {"sql": "select * from `media` where `media`.`model_id` in (1) and `media`.`model_type` = 'App\\\\Models\\\\Language'", "type": "query", "params": [], "bindings": ["App\\Models\\Language"], "hints": null, "show_copy": true, "backtrace": [{"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, {"index": 24, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/MediaCollections/MediaRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\MediaCollections\\MediaRepository.php", "line": 31}, {"index": 25, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 271}, {"index": 26, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 282}, {"index": 27, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 294}], "start": **********.5654602, "duration": 0.0009699999999999999, "duration_str": "970μs", "memory": 0, "memory_str": null, "filename": "InteractsWithMedia.php:545", "source": {"index": 23, "namespace": null, "name": "vendor/spatie/laravel-medialibrary/src/InteractsWithMedia.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\spatie\\laravel-medialibrary\\src\\InteractsWithMedia.php", "line": 545}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FInteractsWithMedia.php&line=545", "ajax": false, "filename": "InteractsWithMedia.php", "line": "545"}, "connection": "kesung_loja", "explain": null, "start_percent": 93.934, "width_percent": 6.066}]}, "models": {"data": {"App\\Models\\Language": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FLanguage.php&line=1", "ajax": false, "filename": "Language.php", "line": "?"}}, "Spatie\\MediaLibrary\\MediaCollections\\Models\\Media": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fspatie%2Flaravel-medialibrary%2Fsrc%2FMediaCollections%2FModels%2FMedia.php&line=1", "ajax": false, "filename": "Media.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/language/show/1", "action_name": "frontend.language.", "controller_action": "App\\Http\\Controllers\\Frontend\\LanguageController@show", "uri": "GET api/frontend/language/show/{language}", "controller": "App\\Http\\Controllers\\Frontend\\LanguageController@show<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FLanguageController.php&line=29\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/language", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FLanguageController.php&line=29\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/LanguageController.php:29-35</a>", "middleware": "api, installed, apiKey, localization", "duration": "487ms", "peak_memory": "50MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1467571243 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1467571243\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1406063943 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1406063943\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2076090059 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjNjY3dzWEFMNHE5U21rV0FRSWhaeEE9PSIsInZhbHVlIjoiL2NESEYxTzBTLzJTbjJQSHhDaWcrVDgwVnZJcW0zSlRuQ1FwcTZiWit5TC9JdmZyL0J3Zi9XOXh6VFJCQXdhVTB4VW9GT2xVL0x2ZDZEa1VXdXlsSzkzdEExT2xCdGEwQ2ZTN1pLb3dYcVV1UEdYaVV5RDgyTi9vSldpTFpDUVgiLCJtYWMiOiIwZjM4MGIxNDRkMzJmMmNhODdmODUzM2IzZjM5OWNkMmM5YmFiOGQ2NGI4YjZmNDFhMmEzMDg0ODg1ZmZmMmI5IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"37 characters\">http://localhost:8000/admin/dashboard</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6IjNjY3dzWEFMNHE5U21rV0FRSWhaeEE9PSIsInZhbHVlIjoiL2NESEYxTzBTLzJTbjJQSHhDaWcrVDgwVnZJcW0zSlRuQ1FwcTZiWit5TC9JdmZyL0J3Zi9XOXh6VFJCQXdhVTB4VW9GT2xVL0x2ZDZEa1VXdXlsSzkzdEExT2xCdGEwQ2ZTN1pLb3dYcVV1UEdYaVV5RDgyTi9vSldpTFpDUVgiLCJtYWMiOiIwZjM4MGIxNDRkMzJmMmNhODdmODUzM2IzZjM5OWNkMmM5YmFiOGQ2NGI4YjZmNDFhMmEzMDg0ODg1ZmZmMmI5IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6Ikt6RjRmeVMyTy9PMjhYMmxYUFpTSVE9PSIsInZhbHVlIjoiTEQ2d1RCVVAybzVkaXZYQ1dZTFFlS1dvdEFCK2laWVFWa3VyT21GVTJOa2NKOHZjUXNNWkIrckJoVWtXdy9HRFF5OXl4MjJFWmNGelVObGM1QklwaGFlVkxMVUZGV1h2djVSeEJxb1d4aVROZFoyRkN2L3ltU3dMR25wWUo4ZWUiLCJtYWMiOiJjODJjYjM4NGY1Y2ViZTQ1MDUwYmI1ZjdiYzk5MThjMjcxYTUzMTkwOGE0ZTIxYTJkOWUzNDEyMWFjMDIyZTBiIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2076090059\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1239184853 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjNjY3dzWEFMNHE5U21rV0FRSWhaeEE9PSIsInZhbHVlIjoiL2NESEYxTzBTLzJTbjJQSHhDaWcrVDgwVnZJcW0zSlRuQ1FwcTZiWit5TC9JdmZyL0J3Zi9XOXh6VFJCQXdhVTB4VW9GT2xVL0x2ZDZEa1VXdXlsSzkzdEExT2xCdGEwQ2ZTN1pLb3dYcVV1UEdYaVV5RDgyTi9vSldpTFpDUVgiLCJtYWMiOiIwZjM4MGIxNDRkMzJmMmNhODdmODUzM2IzZjM5OWNkMmM5YmFiOGQ2NGI4YjZmNDFhMmEzMDg0ODg1ZmZmMmI5IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ikt6RjRmeVMyTy9PMjhYMmxYUFpTSVE9PSIsInZhbHVlIjoiTEQ2d1RCVVAybzVkaXZYQ1dZTFFlS1dvdEFCK2laWVFWa3VyT21GVTJOa2NKOHZjUXNNWkIrckJoVWtXdy9HRFF5OXl4MjJFWmNGelVObGM1QklwaGFlVkxMVUZGV1h2djVSeEJxb1d4aVROZFoyRkN2L3ltU3dMR25wWUo4ZWUiLCJtYWMiOiJjODJjYjM4NGY1Y2ViZTQ1MDUwYmI1ZjdiYzk5MThjMjcxYTUzMTkwOGE0ZTIxYTJkOWUzNDEyMWFjMDIyZTBiIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1239184853\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-430273736 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 02:46:37 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-430273736\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-738681327 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-738681327\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/language/show/1", "action_name": "frontend.language.", "controller_action": "App\\Http\\Controllers\\Frontend\\LanguageController@show"}, "badge": null}}