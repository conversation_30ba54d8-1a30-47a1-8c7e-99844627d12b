<?php $__env->startSection('template_title'); ?>
    <?php echo e(trans('installer.permission.templateTitle')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('title'); ?>
    <?php echo e(trans('installer.permission.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('container'); ?>
    <ul class="installer-track">
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.index')); ?>')" class="done">
            <i class="fa-solid fa-house"></i>
        </li>
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.requirement')); ?>')" class="done">
            <i class="fa-solid fa-server"></i>
        </li>
        <li class="active"><i class="fa-sharp fa-solid fa-unlock"></i></li>
        <li><i class="fa-solid fa-key"></i></li>
        <li><i class="fa-solid fa-gear"></i></li>
        <li><i class="fa-solid fa-database"></i></li>
        <li><i class="fa-solid fa-unlock-keyhole"></i></li>
    </ul>

    <span class="my-6 w-full h-[1px] bg-[#EFF0F6]"></span>

    <ul class="w-full rounded-lg overflow-hidden mb-8 border border-[#D9DBE9]">
        <li class="flex items-center justify-between py-3.5 px-6 border-b border-[#EFF0F6] last:border-none bg-[#F7F7FC]">
            <h3 class="text-sm font-semibold capitalize"><?php echo e(trans('installer.permission.permission_checking')); ?></h3>
        </li>
        <?php $__currentLoopData = $permissions['permissions']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $permission): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
            <li class="flex items-center justify-between py-3.5 px-6 border-b border-[#EFF0F6] last:border-none">
                <span class="text-sm font-medium text-heading"><?php echo e($permission['folder']); ?> -
                    <?php echo e($permission['permission']); ?></span>
                <i
                    class="fa-solid fa-<?php echo e($permission['isSet'] ? 'check-circle' : 'exclamation-circle'); ?> circle-check text-sm text-[#<?php echo e($permission['isSet'] ? '1AB759' : 'E93C3C'); ?>]"></i>
            </li>
        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
    </ul>

    <?php if(!isset($permissions['errors'])): ?>
        <a href="<?php echo e(route('installer.license')); ?>"
            class="w-fit mx-auto p-3 px-6 rounded-lg flex items-center justify-center gap-3 bg-primary text-white">
            <span class="text-sm font-medium capitalize"><?php echo e(trans('installer.permission.next')); ?></span>
            <i class="fa-solid fa-angle-right text-sm"></i>
        </a>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installer.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\resources\views/installer/permission.blade.php ENDPATH**/ ?>