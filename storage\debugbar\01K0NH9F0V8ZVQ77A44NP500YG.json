{"__meta": {"id": "01K0NH9F0V8ZVQ77A44NP500YG", "datetime": "2025-07-21 09:43:55", "utime": **********.933641, "method": "GET", "uri": "/api/frontend/benefit?paginate=0&order_column=id&order_type=asc&status=5", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.524522, "end": **********.933669, "duration": 0.4091470241546631, "duration_str": "409ms", "measures": [{"label": "Booting", "start": **********.524522, "relative_start": 0, "end": **********.81756, "relative_end": **********.81756, "duration": 0.*****************, "duration_str": "293ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.817572, "relative_start": 0.*****************, "end": **********.933673, "relative_end": 3.814697265625e-06, "duration": 0.*****************, "duration_str": "116ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.844848, "relative_start": 0.****************, "end": **********.856058, "relative_end": **********.856058, "duration": 0.011209964752197266, "duration_str": "11.21ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.918862, "relative_start": 0.*****************, "end": **********.928358, "relative_end": **********.928358, "duration": 0.009495973587036133, "duration_str": "9.5ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.928414, "relative_start": 0.*****************, "end": **********.928463, "relative_end": **********.928463, "duration": 4.887580871582031e-05, "duration_str": "49μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01679, "accumulated_duration_str": "16.79ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `benefits` where (`status` like '%5%') order by `id` asc", "type": "query", "params": [], "bindings": ["%5%"], "hints": null, "show_copy": true, "backtrace": [{"index": 15, "namespace": null, "name": "app/Services/BenefitService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\BenefitService.php", "line": 52}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/Frontend/BenefitController.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Http\\Controllers\\Frontend\\BenefitController.php", "line": 23}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 260}], "start": **********.887211, "duration": 0.01679, "duration_str": "16.79ms", "memory": 0, "memory_str": null, "filename": "BenefitService.php:52", "source": {"index": 15, "namespace": null, "name": "app/Services/BenefitService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\BenefitService.php", "line": 52}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FServices%2FBenefitService.php&line=52", "ajax": false, "filename": "BenefitService.php", "line": "52"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/benefit?order_column=id&order_type=asc&paginate=0&status=5", "action_name": "frontend.benefit.", "controller_action": "App\\Http\\Controllers\\Frontend\\BenefitController@index", "uri": "GET api/frontend/benefit", "controller": "App\\Http\\Controllers\\Frontend\\BenefitController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FBenefitController.php&line=20\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/frontend/benefit", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FFrontend%2FBenefitController.php&line=20\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Frontend/BenefitController.php:20-27</a>", "middleware": "api, installed, apiKey, localization", "duration": "546ms", "peak_memory": "52MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1412892258 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>paginate</span>\" => \"<span class=sf-dump-str>0</span>\"\n  \"<span class=sf-dump-key>order_column</span>\" => \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n  \"<span class=sf-dump-key>order_type</span>\" => \"<span class=sf-dump-str title=\"3 characters\">asc</span>\"\n  \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str>5</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1412892258\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1748451013 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1748451013\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1698416383 data-indent-pad=\"  \"><span class=sf-dump-note>array:15</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 1|gHS******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">en</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkxTYURjRmZvTERFL1M0amtSVmJQNFE9PSIsInZhbHVlIjoicGh6R2ZDc3JJT2VZQ3VQTTM0dzBTa2tBWlNzMTFHZFgrOVExNUVZN3VMMEh3MkRBY0kwdFB2bDJBbVNEODYvK0pZQkliNnYxY011MU11VlVKeG8yMHcvbmtEcHRYSkRmeit2TXNtUHlRTGpqTUtlaURnS0FWN0tpK3lnRSt4cmMiLCJtYWMiOiJlNjRlZDdhYjEzM2I5YjA4YTNhZjFiNWZmYjBiOWIwOGUxNzkxODBjYmYxMjFhMmI1N2Q2NjdmNDY1YTVlYzU3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"26 characters\">http://localhost:8000/home</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6IkxTYURjRmZvTERFL1M0amtSVmJQNFE9PSIsInZhbHVlIjoicGh6R2ZDc3JJT2VZQ3VQTTM0dzBTa2tBWlNzMTFHZFgrOVExNUVZN3VMMEh3MkRBY0kwdFB2bDJBbVNEODYvK0pZQkliNnYxY011MU11VlVKeG8yMHcvbmtEcHRYSkRmeit2TXNtUHlRTGpqTUtlaURnS0FWN0tpK3lnRSt4cmMiLCJtYWMiOiJlNjRlZDdhYjEzM2I5YjA4YTNhZjFiNWZmYjBiOWIwOGUxNzkxODBjYmYxMjFhMmI1N2Q2NjdmNDY1YTVlYzU3IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IkFUR0J2aWtQVWgxV1JlSnBqQ01jb2c9PSIsInZhbHVlIjoiWUxhejhhdEZRS3J4YldBYnA5aVdWcUtJM0lTUEtRQXhBaDZORENnUFljUkN2VmplQ0U5cEJBTHIxUTVDKzJ5aE1VRTFFZjZZNzdRRTFUUFA5TllQQXJ2QWM5MklWRWZISTdVZU5TZXA2ak4zNS9lelhXV0d2bTI0Z3lCTVVReVYiLCJtYWMiOiJiMmE4YmY4YzcyMmMwNTFlZTRiMThlZWI2NTdkNGM1MTFjOTg4NzU3NmQyMDJhYWE1NzFlNTkxYTM2ZjBlNTNjIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1698416383\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1537952781 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkxTYURjRmZvTERFL1M0amtSVmJQNFE9PSIsInZhbHVlIjoicGh6R2ZDc3JJT2VZQ3VQTTM0dzBTa2tBWlNzMTFHZFgrOVExNUVZN3VMMEh3MkRBY0kwdFB2bDJBbVNEODYvK0pZQkliNnYxY011MU11VlVKeG8yMHcvbmtEcHRYSkRmeit2TXNtUHlRTGpqTUtlaURnS0FWN0tpK3lnRSt4cmMiLCJtYWMiOiJlNjRlZDdhYjEzM2I5YjA4YTNhZjFiNWZmYjBiOWIwOGUxNzkxODBjYmYxMjFhMmI1N2Q2NjdmNDY1YTVlYzU3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IkFUR0J2aWtQVWgxV1JlSnBqQ01jb2c9PSIsInZhbHVlIjoiWUxhejhhdEZRS3J4YldBYnA5aVdWcUtJM0lTUEtRQXhBaDZORENnUFljUkN2VmplQ0U5cEJBTHIxUTVDKzJ5aE1VRTFFZjZZNzdRRTFUUFA5TllQQXJ2QWM5MklWRWZISTdVZU5TZXA2ak4zNS9lelhXV0d2bTI0Z3lCTVVReVYiLCJtYWMiOiJiMmE4YmY4YzcyMmMwNTFlZTRiMThlZWI2NTdkNGM1MTFjOTg4NzU3NmQyMDJhYWE1NzFlNTkxYTM2ZjBlNTNjIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1537952781\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-739843102 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:43:55 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-739843102\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-669192944 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-669192944\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/frontend/benefit?order_column=id&order_type=asc&paginate=0&status=5", "action_name": "frontend.benefit.", "controller_action": "App\\Http\\Controllers\\Frontend\\BenefitController@index"}, "badge": null}}