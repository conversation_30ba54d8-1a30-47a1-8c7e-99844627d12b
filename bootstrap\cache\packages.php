<?php return array (
  'anandsiddharth/laravel-paytm-wallet' => 
  array (
    'aliases' => 
    array (
      'PaytmWallet' => '<PERSON>\\LaravelPaytmWallet\\Facades\\PaytmWallet',
    ),
    'providers' => 
    array (
      0 => 'Anand\\LaravelPaytmWallet\\PaytmWalletServiceProvider',
    ),
  ),
  'aws/aws-sdk-php-laravel' => 
  array (
    'aliases' => 
    array (
      'AWS' => 'Aws\\Laravel\\AwsFacade',
    ),
    'providers' => 
    array (
      0 => 'Aws\\Laravel\\AwsServiceProvider',
    ),
  ),
  'barryvdh/laravel-debugbar' => 
  array (
    'aliases' => 
    array (
      'Debugbar' => 'Barryvdh\\Debugbar\\Facades\\Debugbar',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\Debugbar\\ServiceProvider',
    ),
  ),
  'barryvdh/laravel-dompdf' => 
  array (
    'aliases' => 
    array (
      'PDF' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
      'Pdf' => 'Barryvdh\\DomPDF\\Facade\\Pdf',
    ),
    'providers' => 
    array (
      0 => 'Barryvdh\\DomPDF\\ServiceProvider',
    ),
  ),
  'dipesh79/laravel-phonepe' => 
  array (
    'providers' => 
    array (
      0 => 'Dipesh79\\LaravelPhonePe\\LaravelPhonePeServiceProvider',
    ),
  ),
  'dipokhalder/laravel-env-editor' => 
  array (
    'aliases' => 
    array (
      'EnvEditor' => 'Dipokhalder\\EnvEditor\\EnvEditorFacade',
    ),
    'providers' => 
    array (
      0 => 'Dipokhalder\\EnvEditor\\EnvEditorServiceProvider',
    ),
  ),
  'intervention/image' => 
  array (
    'aliases' => 
    array (
      'Image' => 'Intervention\\Image\\Facades\\Image',
    ),
    'providers' => 
    array (
      0 => 'Intervention\\Image\\ImageServiceProvider',
    ),
  ),
  'karim007/laravel-bkash-tokenize' => 
  array (
    'aliases' => 
    array (
      'LaravelBkashTokenize' => 'Karim007\\LaravelBkashTokenize\\LaravelBkashTokenize',
    ),
    'providers' => 
    array (
      0 => 'Karim007\\LaravelBkashTokenize\\BkashTokenizeServiceProvider',
    ),
  ),
  'kingflamez/laravelrave' => 
  array (
    'aliases' => 
    array (
      'Rave' => 'KingFlamez\\Rave\\Facades\\Rave',
    ),
    'providers' => 
    array (
      0 => 'KingFlamez\\Rave\\RaveServiceProvider',
    ),
  ),
  'laravel/sail' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sail\\SailServiceProvider',
    ),
  ),
  'laravel/sanctum' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Sanctum\\SanctumServiceProvider',
    ),
  ),
  'laravel/tinker' => 
  array (
    'providers' => 
    array (
      0 => 'Laravel\\Tinker\\TinkerServiceProvider',
    ),
  ),
  'maatwebsite/excel' => 
  array (
    'aliases' => 
    array (
      'Excel' => 'Maatwebsite\\Excel\\Facades\\Excel',
    ),
    'providers' => 
    array (
      0 => 'Maatwebsite\\Excel\\ExcelServiceProvider',
    ),
  ),
  'mollie/laravel-mollie' => 
  array (
    'aliases' => 
    array (
      'Mollie' => 'Mollie\\Laravel\\Facades\\Mollie',
    ),
    'providers' => 
    array (
      0 => 'Mollie\\Laravel\\MollieServiceProvider',
    ),
  ),
  'nesbot/carbon' => 
  array (
    'providers' => 
    array (
      0 => 'Carbon\\Laravel\\ServiceProvider',
    ),
  ),
  'nunomaduro/collision' => 
  array (
    'providers' => 
    array (
      0 => 'NunoMaduro\\Collision\\Adapters\\Laravel\\CollisionServiceProvider',
    ),
  ),
  'nunomaduro/termwind' => 
  array (
    'providers' => 
    array (
      0 => 'Termwind\\Laravel\\TermwindServiceProvider',
    ),
  ),
  'nyawach/laravel-pesapal' => 
  array (
    'aliases' => 
    array (
      'LaravelPesapal' => 'Nyawach\\LaravelPesapal\\Facades',
    ),
    'providers' => 
    array (
      0 => 'Nyawach\\LaravelPesapal\\PesaPalServiceProvider',
    ),
  ),
  'obydul/laraskrill' => 
  array (
    'providers' => 
    array (
      0 => 'Obydul\\LaraSkrill\\LaraSkrillServiceProvider',
    ),
  ),
  'silviolleite/laravelpwa' => 
  array (
    'providers' => 
    array (
      0 => 'LaravelPWA\\Providers\\LaravelPWAServiceProvider',
    ),
  ),
  'smartisan/laravel-settings' => 
  array (
    'aliases' => 
    array (
      'Settings' => 'Smartisan\\Settings\\Facades\\Settings',
    ),
    'providers' => 
    array (
      0 => 'Smartisan\\Settings\\SettingsServiceProvider',
    ),
  ),
  'spatie/laravel-ignition' => 
  array (
    'aliases' => 
    array (
      'Flare' => 'Spatie\\LaravelIgnition\\Facades\\Flare',
    ),
    'providers' => 
    array (
      0 => 'Spatie\\LaravelIgnition\\IgnitionServiceProvider',
    ),
  ),
  'spatie/laravel-medialibrary' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\MediaLibrary\\MediaLibraryServiceProvider',
    ),
  ),
  'spatie/laravel-permission' => 
  array (
    'providers' => 
    array (
      0 => 'Spatie\\Permission\\PermissionServiceProvider',
    ),
  ),
  'srmklive/paypal' => 
  array (
    'aliases' => 
    array (
      'PayPal' => 'Srmklive\\PayPal\\Facades\\PayPal',
    ),
    'providers' => 
    array (
      0 => 'Srmklive\\PayPal\\Providers\\PayPalServiceProvider',
    ),
  ),
  'staudenmeir/laravel-adjacency-list' => 
  array (
    'providers' => 
    array (
      0 => 'Staudenmeir\\LaravelAdjacencyList\\IdeHelperServiceProvider',
    ),
  ),
  'staudenmeir/laravel-cte' => 
  array (
    'providers' => 
    array (
      0 => 'Staudenmeir\\LaravelCte\\DatabaseServiceProvider',
    ),
  ),
  'unicodeveloper/laravel-paystack' => 
  array (
    'aliases' => 
    array (
      'Paystack' => 'Unicodeveloper\\Paystack\\Facades\\Paystack',
    ),
    'providers' => 
    array (
      0 => 'Unicodeveloper\\Paystack\\PaystackServiceProvider',
    ),
  ),
);