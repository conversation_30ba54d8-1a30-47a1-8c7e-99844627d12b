<?php
/**
 * Teste simples da API do português
 */

echo "=== TESTE DA API PORTUGUÊS ===\n\n";

$url = 'http://localhost:8000/api/frontend/language/show/4';
$apiKey = 'SHOPPERZZ-2025-FREE-LICENSE';

echo "🔑 API Key: {$apiKey}\n";
echo "🌐 URL: {$url}\n\n";

// Usar cURL para fazer a requisição
$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'x-api-key: ' . $apiKey,
    'Accept: application/json',
    'Content-Type: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 30);

echo "📡 Fazendo requisição...\n";

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

if ($error) {
    echo "❌ Erro cURL: {$error}\n";
} else {
    echo "📊 Status HTTP: {$httpCode}\n";
    
    if ($httpCode == 200) {
        echo "✅ Sucesso!\n\n";
        echo "📄 Resposta:\n";
        
        $data = json_decode($response, true);
        if ($data) {
            echo json_encode($data, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE) . "\n";
            
            if (isset($data['data'])) {
                echo "\n🇧🇷 PORTUGUÊS ENCONTRADO:\n";
                $lang = $data['data'];
                echo "   ID: " . ($lang['id'] ?? 'N/A') . "\n";
                echo "   Nome: " . ($lang['name'] ?? 'N/A') . "\n";
                echo "   Código: " . ($lang['code'] ?? 'N/A') . "\n";
                echo "   Status: " . ($lang['status'] == 5 ? 'ATIVO' : 'INATIVO') . "\n";
            }
        } else {
            echo $response . "\n";
        }
    } else {
        echo "❌ Erro HTTP {$httpCode}\n";
        echo "Resposta: {$response}\n";
    }
}

echo "\n=== TESTE LISTA DE IDIOMAS ===\n";

$urlList = 'http://localhost:8000/api/frontend/language';
echo "🌐 URL: {$urlList}\n";

$ch2 = curl_init();
curl_setopt($ch2, CURLOPT_URL, $urlList);
curl_setopt($ch2, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch2, CURLOPT_HTTPHEADER, [
    'x-api-key: ' . $apiKey,
    'Accept: application/json'
]);
curl_setopt($ch2, CURLOPT_TIMEOUT, 30);

$response2 = curl_exec($ch2);
$httpCode2 = curl_getinfo($ch2, CURLINFO_HTTP_CODE);
$error2 = curl_error($ch2);

curl_close($ch2);

if ($error2) {
    echo "❌ Erro cURL: {$error2}\n";
} else {
    echo "📊 Status HTTP: {$httpCode2}\n";
    
    if ($httpCode2 == 200) {
        echo "✅ Lista obtida com sucesso!\n\n";
        
        $dataList = json_decode($response2, true);
        if ($dataList && isset($dataList['data'])) {
            echo "📋 Idiomas disponíveis:\n";
            foreach ($dataList['data'] as $lang) {
                $status = $lang['status'] == 5 ? 'ATIVO' : 'INATIVO';
                echo "   ID: {$lang['id']} | {$lang['name']} ({$lang['code']}) - {$status}\n";
            }
        }
    } else {
        echo "❌ Erro HTTP {$httpCode2}\n";
        echo "Resposta: {$response2}\n";
    }
}

echo "\n=== FIM DO TESTE ===\n";
?>
