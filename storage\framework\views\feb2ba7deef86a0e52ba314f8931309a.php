<?php $__env->startSection('template_title'); ?>
    <?php echo e(trans('installer.welcome.templateTitle')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('title'); ?>
    <?php echo e(trans('installer.welcome.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('container'); ?>
    <ul class="installer-track">
        <li class="active"><i class="fa-solid fa-house"></i></li>
        <li><i class="fa-solid fa-server"></i></li>
        <li><i class="fa-sharp fa-solid fa-unlock"></i></li>
        <li><i class="fa-solid fa-key"></i></li>
        <li><i class="fa-solid fa-gear"></i></li>
        <li><i class="fa-solid fa-database"></i></li>
        <li><i class="fa-solid fa-unlock-keyhole"></i></li>
    </ul>

    <span class="my-6 w-full h-[1px] bg-[#EFF0F6]"></span>

    <div class="text-center">
        <h4 class="text-sm font-semibold mb-7"><?php echo e(trans('installer.welcome.message')); ?></h4>
        <a href="<?php echo e(route('installer.requirement')); ?>"
            class="p-3 px-6 rounded-lg inline-flex items-center justify-center gap-3 bg-primary text-white">
            <?php echo e(trans('installer.welcome.next')); ?>

            <i class="fa-solid fa-angle-right text-sm"></i>
        </a>
    </div>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installer.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\resources\views/installer/welcome.blade.php ENDPATH**/ ?>