<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Carbon\Carbon;

class PortugueseLanguageSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Check if Portuguese language already exists
        $existingLanguage = DB::table('languages')->where('code', 'pt')->first();

        if (!$existingLanguage) {
            // Insert Portuguese language
            $languageId = DB::table('languages')->insertGetId([
                'name' => 'Português',
                'code' => 'pt',
                'display_mode' => 1, // LTR
                'status' => 1, // Active
                'created_at' => Carbon::now(),
                'updated_at' => Carbon::now(),
            ]);

            echo "✅ Idioma Português adicionado com ID: {$languageId}\n";
        } else {
            echo "⚠️ Idioma Português já existe no banco de dados.\n";
            $languageId = $existingLanguage->id;
        }

        // Create language files if they don't exist
        $jsLanguageFile = base_path("resources/js/languages/pt.json");
        $langDirectory = base_path("lang/pt");

        // Copy English language files as base for Portuguese
        if (!file_exists($jsLanguageFile)) {
            $englishFile = base_path("resources/js/languages/en.json");
            if (file_exists($englishFile)) {
                copy($englishFile, $jsLanguageFile);
                echo "✅ Arquivo de tradução JavaScript criado: pt.json\n";
            }
        }

        // Create Laravel language directory and files
        if (!file_exists($langDirectory)) {
            mkdir($langDirectory, 0755, true);

            // Copy English language files
            $englishDir = base_path("lang/en");
            if (is_dir($englishDir)) {
                $files = scandir($englishDir);
                foreach ($files as $file) {
                    if ($file != '.' && $file != '..') {
                        copy("{$englishDir}/{$file}", "{$langDirectory}/{$file}");
                    }
                }
                echo "✅ Arquivos de tradução Laravel criados no diretório: lang/pt/\n";
            }
        }

        echo "✅ Arquivos de idioma português configurados com sucesso!\n";
        echo "🎉 Configuração do idioma português concluída!\n";
    }
}
