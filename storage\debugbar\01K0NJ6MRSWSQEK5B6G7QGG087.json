{"__meta": {"id": "01K0NJ6MRSWSQEK5B6G7QGG087", "datetime": "2025-07-21 09:59:52", "utime": **********.090532, "method": "GET", "uri": "/api/admin/setting/company", "ip": "::1"}, "messages": {"count": 0, "messages": []}, "time": {"start": **********.720315, "end": **********.090547, "duration": 0.37023210525512695, "duration_str": "370ms", "measures": [{"label": "Booting", "start": **********.720315, "relative_start": 0, "end": **********.978144, "relative_end": **********.978144, "duration": 0.***************, "duration_str": "258ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.978155, "relative_start": 0.****************, "end": **********.090549, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "112ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.001226, "relative_start": 0.****************, "end": **********.004624, "relative_end": **********.004624, "duration": 0.0033979415893554688, "duration_str": "3.4ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.082853, "relative_start": 0.*****************, "end": **********.088188, "relative_end": **********.088188, "duration": 0.0053348541259765625, "duration_str": "5.33ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.088212, "relative_start": 0.*****************, "end": **********.088236, "relative_end": **********.088236, "duration": 2.4080276489257812e-05, "duration_str": "24μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "38MB"}, "exceptions": {"count": 0, "exceptions": []}, "laravel": {"version": "10.x", "tooltip": {"Laravel Version": "10.48.29", "PHP Version": "8.1.33", "Environment": "local", "Debug Mode": "Enabled", "URL": "localhost:8000", "Timezone": "Asia/Dhaka", "Locale": "en"}}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "queries": {"count": 4, "nb_statements": 4, "nb_visible_statements": 4, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.02054, "accumulated_duration_str": "20.54ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `personal_access_tokens` where `personal_access_tokens`.`id` = '2' limit 1", "type": "query", "params": [], "bindings": ["2"], "hints": null, "show_copy": true, "backtrace": [{"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 67}, {"index": 24, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 25, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.026378, "duration": 0.016739999999999998, "duration_str": "16.74ms", "memory": 0, "memory_str": null, "filename": "PersonalAccessToken.php:66", "source": {"index": 20, "namespace": null, "name": "vendor/laravel/sanctum/src/PersonalAccessToken.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\PersonalAccessToken.php", "line": 66}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=66", "ajax": false, "filename": "PersonalAccessToken.php", "line": "66"}, "connection": "kesung_loja", "explain": null, "start_percent": 0, "width_percent": 81.5}, {"sql": "select * from `users` where `users`.`id` = 1 and `users`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [1], "hints": null, "show_copy": true, "backtrace": [{"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, {"index": 22, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 69}, {"index": 25, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 26, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 27, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.0569692, "duration": 0.00063, "duration_str": "630μs", "memory": 0, "memory_str": null, "filename": "Guard.php:161", "source": {"index": 21, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 161}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FGuard.php&line=161", "ajax": false, "filename": "Guard.php", "line": "161"}, "connection": "kesung_loja", "explain": null, "start_percent": 81.5, "width_percent": 3.067}, {"sql": "update `personal_access_tokens` set `last_used_at` = '2025-07-21 09:59:52', `personal_access_tokens`.`updated_at` = '2025-07-21 09:59:52' where `id` = 2", "type": "query", "params": [], "bindings": ["2025-07-21 09:59:52", "2025-07-21 09:59:52", 2], "hints": null, "show_copy": true, "backtrace": [{"index": 10, "namespace": null, "name": "vendor/staudenmeir/laravel-cte/src/Query/Traits/BuildsExpressionQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-cte\\src\\Query\\Traits\\BuildsExpressionQueries.php", "line": 225}, {"index": 14, "namespace": null, "name": "vendor/laravel/sanctum/src/Guard.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\sanctum\\src\\Guard.php", "line": 83}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Auth/GuardHelpers.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\GuardHelpers.php", "line": 56}, {"index": 20, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 76}, {"index": 21, "namespace": "middleware", "name": "auth", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php", "line": 55}], "start": **********.063804, "duration": 0.0023599999999999997, "duration_str": "2.36ms", "memory": 0, "memory_str": null, "filename": "BuildsExpressionQueries.php:225", "source": {"index": 10, "namespace": null, "name": "vendor/staudenmeir/laravel-cte/src/Query/Traits/BuildsExpressionQueries.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\staudenmeir\\laravel-cte\\src\\Query\\Traits\\BuildsExpressionQueries.php", "line": 225}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fstaudenmeir%2Flaravel-cte%2Fsrc%2FQuery%2FTraits%2FBuildsExpressionQueries.php&line=225", "ajax": false, "filename": "BuildsExpressionQueries.php", "line": "225"}, "connection": "kesung_loja", "explain": null, "start_percent": 84.567, "width_percent": 11.49}, {"sql": "select `payload`, `key` from `settings` where `group` = 'company' and (`settingable_type` is null and `settingable_id` is null)", "type": "query", "params": [], "bindings": ["company"], "hints": null, "show_copy": true, "backtrace": [{"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, {"index": 14, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 109}, {"index": 15, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 179}, {"index": 16, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Settings.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Settings.php", "line": 95}, {"index": 17, "namespace": null, "name": "app/Services/CompanyService.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\app\\Services\\CompanyService.php", "line": 29}], "start": **********.076188, "duration": 0.0008100000000000001, "duration_str": "810μs", "memory": 0, "memory_str": null, "filename": "DatabaseRepository.php:143", "source": {"index": 13, "namespace": null, "name": "vendor/smartisan/laravel-settings/src/Repositories/DatabaseRepository.php", "file": "C:\\Users\\<USER>\\Desktop\\xamp8.1\\htdocs\\vendor\\smartisan\\laravel-settings\\src\\Repositories\\DatabaseRepository.php", "line": 143}, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Fsmartisan%2Flaravel-settings%2Fsrc%2FRepositories%2FDatabaseRepository.php&line=143", "ajax": false, "filename": "DatabaseRepository.php", "line": "143"}, "connection": "kesung_loja", "explain": null, "start_percent": 96.056, "width_percent": 3.944}]}, "models": {"data": {"Laravel\\Sanctum\\PersonalAccessToken": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fvendor%2Flaravel%2Fsanctum%2Fsrc%2FPersonalAccessToken.php&line=1", "ajax": false, "filename": "PersonalAccessToken.php", "line": "?"}}, "App\\Models\\User": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FModels%2FUser.php&line=1", "ajax": false, "filename": "User.php", "line": "?"}}}, "count": 2, "is_counter": true}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "request": {"data": {"status": "200 OK", "full_url": "http://localhost:8000/api/admin/setting/company", "action_name": "admin.setting.company.", "controller_action": "App\\Http\\Controllers\\Admin\\CompanyController@index", "uri": "GET api/admin/setting/company", "controller": "App\\Http\\Controllers\\Admin\\CompanyController@index<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FCompanyController.php&line=21\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "api/admin/setting/company", "file": "<a href=\"phpstorm://open?file=C%3A%2FUsers%2Fyakus%2FDesktop%2Fxamp8.1%2Fhtdocs%2Fapp%2FHttp%2FControllers%2FAdmin%2FCompanyController.php&line=21\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/Admin/CompanyController.php:21-28</a>", "middleware": "api, auth:sanctum", "duration": "483ms", "peak_memory": "54MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-2111236147 data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">localhost:8000</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"80 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:140.0) Gecko/20100101 Firefox/140.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"35 characters\">pt-BR,pt;q=0.8,en-US;q=0.5,en;q=0.3</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-api-key</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"27 characters\">SHOPPERZZ-2025-FREE-LICENSE</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>authorization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">Bearer 2|thT******</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-localization</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">pt</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-xsrf-token</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0=</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"31 characters\">http://localhost:8000/admin/pos</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1140 characters\">XSRF-TOKEN=eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0%3D; shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session=eyJpdiI6IjRzUXZSMWFqSmdIWng1ci9BWE9xR3c9PSIsInZhbHVlIjoiUHIwaWNDRlFLR3N5RHZldHdmS2w3ZzcvdjBiZkFRMkZLTGJaWFRZbXhQREJxMHh0VkV0SGYrK1B4NlRxdzc4QytrNXg3SE94N3ZGd1hVYzd6SUp5TWpGWWc4OFpZdHdsNmFWWFdjMjJDZEhFdktTZ2IvMVVrc3pMZ20rRnU3aEYiLCJtYWMiOiJlNTUyNWM5NTRkODgxMjA0MDNhMzFkYTFhNDI2MzZmMjliNjAwMWI0MzM4YjBiMWQxNGFiNjViNDQ3ZmM5ZGVkIiwidGFnIjoiIn0%3D; kesung_session=eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>priority</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">u=0</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-2111236147\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-1439965905 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ik9uaUxYSkQ4UlpnQkRCdnBSMGVpTWc9PSIsInZhbHVlIjoiY3c0UWUrMzg3aElGN3V5MHpoQWZlVHRSZkNOd3RVbjJGb2tOSCtGOGxGZFhsS1J2Si96MWxnYm41Z1JzWThZaGVkWkpLLzRKcmg3VlFxQ29HTXVSTWxWNVMyQldDNXR4MTVlbnlwNmk2ZmNPQ1ZUU2NtNm9VSXVlbXJZTkllbWQiLCJtYWMiOiI2NTNkOWJiNDBlYWY4ZTIyMDg4NDdkYjQ4OTNkY2Y4MzdhOGY2ZTMzYzM5NWE0MzQzZDc3NzRjYWRjNTVhMDQ3IiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>shopperzz_pwa_ecommerce_cms_with_pos_whatsapp_ordering_inventory_management_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6IjRzUXZSMWFqSmdIWng1ci9BWE9xR3c9PSIsInZhbHVlIjoiUHIwaWNDRlFLR3N5RHZldHdmS2w3ZzcvdjBiZkFRMkZLTGJaWFRZbXhQREJxMHh0VkV0SGYrK1B4NlRxdzc4QytrNXg3SE94N3ZGd1hVYzd6SUp5TWpGWWc4OFpZdHdsNmFWWFdjMjJDZEhFdktTZ2IvMVVrc3pMZ20rRnU3aEYiLCJtYWMiOiJlNTUyNWM5NTRkODgxMjA0MDNhMzFkYTFhNDI2MzZmMjliNjAwMWI0MzM4YjBiMWQxNGFiNjViNDQ3ZmM5ZGVkIiwidGFnIjoiIn0=</span>\"\n  \"<span class=sf-dump-key>kesung_session</span>\" => \"<span class=sf-dump-str title=\"340 characters\">eyJpdiI6Ilprd0NVbHZDa1Zzb2VOeVNtVEtqSXc9PSIsInZhbHVlIjoiRHlwZVhEYW5SeXNvNm5HYU53dnhCU0pQb0ZEV0c4UXVIY2ZTZEt5TGQ4TWdwYlNMQXlHSW9NVE9VZWwzdVlVdHBPblBCNVRQS3lPOGZMVUxSbGN1MCtqQ20wakV5SUR6MFYzRHJzYjFhVnBWdGo2alZoTi9LS1Z4Um5LTHhmTTEiLCJtYWMiOiJhMjkwYWYxMzZiYjZlNmIyNzVmM2M4ZGMwMmI0NDExMWQ3NDFjOTAwY2M0ZDQ4NmI1ODEyOTlmYjZjMWY1MmFhIiwidGFnIjoiIn0=</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1439965905\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-1146888145 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 21 Jul 2025 03:59:52 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1146888145\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-********* data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-*********\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "http://localhost:8000/api/admin/setting/company", "action_name": "admin.setting.company.", "controller_action": "App\\Http\\Controllers\\Admin\\CompanyController@index"}, "badge": null}}