<?php $__env->startSection('template_title'); ?>
    <?php echo e(trans('installer.requirement.templateTitle')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('title'); ?>
    <?php echo e(trans('installer.requirement.title')); ?>

<?php $__env->stopSection(); ?>

<?php $__env->startSection('container'); ?>
    <ul class="installer-track">
        <li onclick="handleLinkForInstaller('<?php echo e(route('installer.index')); ?>')" class="done">
            <i class="fa-solid fa-house"></i>
        </li>
        <li class="active"><i class="fa-solid fa-server"></i></li>
        <li><i class="fa-sharp fa-solid fa-unlock"></i></li>
        <li><i class="fa-solid fa-key"></i></li>
        <li><i class="fa-solid fa-gear"></i></li>
        <li><i class="fa-solid fa-database"></i></li>
        <li><i class="fa-solid fa-unlock-keyhole"></i></li>
    </ul>

    <span class="my-6 w-full h-[1px] bg-[#EFF0F6]"></span>

    <?php $__currentLoopData = $requirements['requirements']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $type => $requirement): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
        <ul class="w-full rounded-lg overflow-hidden mb-8 border border-[#D9DBE9]">
            <li class="flex items-center justify-between gap-2 py-3.5 px-6 border-b border-[#EFF0F6] last:border-none bg-[#F7F7FC]">
                <?php if($type == 'php'): ?>
                    <h3 class="text-sm font-semibold capitalize"><?php echo e(ucfirst($type)); ?>

                        <span class="text-xs font-medium lowercase">( <?php echo e(trans('installer.requirement.version')); ?> <?php echo e($phpSupportInfo['minimum']); ?> <?php echo e(trans('installer.requirement.required')); ?>)</span>
                    </h3>
                    <span class="flex items-center gap-1 text-[#1AB759]">
                    <span class="text-sm font-semibold"><?php echo e($phpSupportInfo['current']); ?></span>
                    <i class="fa-solid fa-<?php echo e($phpSupportInfo['supported'] ? 'check-circle' : 'exclamation-circle'); ?> text-sm text-[#<?php echo e($phpSupportInfo['supported'] ? '1AB759' : 'E93C3C'); ?>]"></i>
                </span>
                <?php endif; ?>
            </li>

            <?php $__currentLoopData = $requirements['requirements'][$type]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $extension => $enabled): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <li class="flex items-center justify-between py-3.5 px-6 border-b border-[#EFF0F6] last:border-none">
                    <span class="text-sm font-medium capitalize text-heading"><?php echo e($extension); ?></span>
                    <i class="fa-solid fa-<?php echo e($enabled ? 'circle-check' : 'exclamation-circle'); ?> text-sm text-[#<?php echo e($enabled ? '1AB759' : 'E93C3C'); ?>]"></i>
                </li>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </ul>
    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

    <?php if( ! isset($requirements['errors']) && $phpSupportInfo['supported'] ): ?>
        <a href="<?php echo e(route('installer.permission')); ?>"
           class="w-fit mx-auto p-3 px-6 rounded-lg flex items-center justify-center gap-3 bg-primary text-white">
            <span class="text-sm font-medium capitalize"><?php echo e(trans('installer.requirement.next')); ?></span>
            <i class="fa-solid fa-angle-right text-sm"></i>
        </a>
    <?php endif; ?>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('installer.layouts.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\Users\<USER>\Desktop\xamp8.1\htdocs\resources\views/installer/requirement.blade.php ENDPATH**/ ?>