<?php
/**
 * Debug da API Key
 */

require 'vendor/autoload.php';

$app = require_once 'bootstrap/app.php';
$app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();

echo "=== DEBUG DA API KEY ===\n\n";

// Verificar valor no .env
echo "🔍 Verificando valores da API Key:\n\n";

echo "1. env('MIX_API_KEY'): " . (env('MIX_API_KEY') ?: 'NULL/VAZIO') . "\n";
echo "2. config('app.mix_api_key'): " . (config('app.mix_api_key') ?: 'NULL/VAZIO') . "\n";

// Ler diretamente do arquivo .env
$envFile = base_path('.env');
if (file_exists($envFile)) {
    $envContent = file_get_contents($envFile);
    if (preg_match('/MIX_API_KEY=(.*)/', $envContent, $matches)) {
        echo "3. Arquivo .env: " . trim($matches[1]) . "\n";
    } else {
        echo "3. Arquivo .env: NÃO ENCONTRADO\n";
    }
} else {
    echo "3. Arquivo .env: ARQUIVO NÃO EXISTE\n";
}

// Verificar cache de configuração
$configCacheFile = base_path('bootstrap/cache/config.php');
if (file_exists($configCacheFile)) {
    echo "4. Cache de config: EXISTE (pode estar interferindo)\n";
} else {
    echo "4. Cache de config: NÃO EXISTE\n";
}

echo "\n=== TESTE DO MIDDLEWARE ===\n";

// Simular o que o middleware faz
$testKey = 'SHOPPERZZ-2025-FREE-LICENSE';
$envKey = env('MIX_API_KEY');

echo "Chave de teste: {$testKey}\n";
echo "Chave do env(): " . ($envKey ?: 'NULL/VAZIO') . "\n";
echo "Comparação: " . ($testKey == $envKey ? 'IGUAL ✅' : 'DIFERENTE ❌') . "\n";

echo "\n=== CORREÇÃO ===\n";

// Tentar corrigir
try {
    // Limpar cache primeiro
    Artisan::call('config:clear');
    echo "✅ Cache de configuração limpo\n";
    
    // Verificar novamente
    $envKeyAfter = env('MIX_API_KEY');
    echo "Chave após limpar cache: " . ($envKeyAfter ?: 'NULL/VAZIO') . "\n";
    echo "Comparação após cache: " . ($testKey == $envKeyAfter ? 'IGUAL ✅' : 'DIFERENTE ❌') . "\n";
    
    if ($testKey != $envKeyAfter) {
        echo "\n🔧 Tentando corrigir no arquivo .env...\n";
        
        // Ler e corrigir o arquivo .env
        if (file_exists($envFile)) {
            $envContent = file_get_contents($envFile);
            
            // Substituir ou adicionar MIX_API_KEY
            if (preg_match('/MIX_API_KEY=.*/', $envContent)) {
                $envContent = preg_replace('/MIX_API_KEY=.*/', 'MIX_API_KEY=SHOPPERZZ-2025-FREE-LICENSE', $envContent);
                echo "✅ MIX_API_KEY atualizada no .env\n";
            } else {
                $envContent .= "\nMIX_API_KEY=SHOPPERZZ-2025-FREE-LICENSE\n";
                echo "✅ MIX_API_KEY adicionada ao .env\n";
            }
            
            file_put_contents($envFile, $envContent);
            
            // Limpar cache novamente
            Artisan::call('config:clear');
            echo "✅ Cache limpo após correção\n";
            
            // Verificar final
            $envKeyFinal = env('MIX_API_KEY');
            echo "Chave final: " . ($envKeyFinal ?: 'NULL/VAZIO') . "\n";
            echo "Comparação final: " . ($testKey == $envKeyFinal ? 'IGUAL ✅' : 'DIFERENTE ❌') . "\n";
        }
    }
    
} catch (Exception $e) {
    echo "❌ Erro na correção: " . $e->getMessage() . "\n";
}

echo "\n=== TESTE FINAL DA API ===\n";

// Fazer um teste real da API
$url = 'http://localhost:8000/api/frontend/language/show/4';
$apiKey = env('MIX_API_KEY') ?: 'SHOPPERZZ-2025-FREE-LICENSE';

echo "🌐 Testando: {$url}\n";
echo "🔑 Com chave: {$apiKey}\n";

$ch = curl_init();
curl_setopt($ch, CURLOPT_URL, $url);
curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
curl_setopt($ch, CURLOPT_HTTPHEADER, [
    'x-api-key: ' . $apiKey,
    'Accept: application/json'
]);
curl_setopt($ch, CURLOPT_TIMEOUT, 10);

$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
curl_close($ch);

echo "📊 Status: {$httpCode}\n";

if ($httpCode == 200) {
    echo "✅ API FUNCIONANDO!\n";
    $data = json_decode($response, true);
    if (isset($data['data']['name'])) {
        echo "🇧🇷 Idioma: " . $data['data']['name'] . "\n";
    }
} else {
    echo "❌ API ainda com erro\n";
    echo "Resposta: {$response}\n";
}

echo "\n=== FIM DO DEBUG ===\n";
?>
